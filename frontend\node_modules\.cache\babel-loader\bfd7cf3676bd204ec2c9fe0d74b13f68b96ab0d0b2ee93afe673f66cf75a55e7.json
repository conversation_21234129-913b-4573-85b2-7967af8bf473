{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\AIAssistant.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, Typography, Button, Input, Select, Row, Col, Tabs, Form, message, Spin, Space, Tag, Divider, Alert, Modal, List, Slider, Switch, InputNumber, Badge, Popconfirm, Drawer } from 'antd';\nimport { RobotOutlined, MessageOutlined, SendOutlined, BulbOutlined, UserOutlined, ReloadOutlined, BookOutlined, EditOutlined, CheckCircleOutlined, HistoryOutlined, DownloadOutlined, CopyOutlined, DeleteOutlined, SettingOutlined, FileTextOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst AIAssistant = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [providers, setProviders] = useState([]);\n  const [currentProvider, setCurrentProvider] = useState('');\n  const [aiStatus, setAiStatus] = useState({\n    connected: false,\n    status: 'offline'\n  });\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [generateForm] = Form.useForm();\n  const [plotForm] = Form.useForm();\n  const [continueForm] = Form.useForm();\n  const [checkForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('chat');\n  const messagesEndRef = useRef(null);\n\n  // 新增状态\n  const [generatedContent, setGeneratedContent] = useState('');\n  const [contentHistory, setContentHistory] = useState([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const [showTemplates, setShowTemplates] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [templates, setTemplates] = useState([]);\n  const [aiParams, setAiParams] = useState({\n    temperature: 0.7,\n    max_tokens: 2000,\n    top_p: 1.0,\n    frequency_penalty: 0.0,\n    presence_penalty: 0.0\n  });\n  const [batchMode, setBatchMode] = useState(false);\n  const [batchCount, setBatchCount] = useState(3);\n\n  // 获取AI提供商列表\n  const fetchProviders = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/providers');\n      setProviders(response.data.providers);\n      setCurrentProvider(response.data.current);\n    } catch (error) {\n      message.error('获取AI提供商列表失败');\n    }\n  };\n\n  // 获取AI状态\n  const fetchAIStatus = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n    } catch (error) {\n      setAiStatus({\n        connected: false,\n        status: 'error'\n      });\n    }\n  };\n\n  // 切换AI提供商\n  const switchProvider = async provider => {\n    try {\n      setLoading(true);\n      await axios.post('/api/v1/ai/switch-provider', {\n        provider\n      });\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${provider}`);\n      await fetchAIStatus();\n    } catch (error) {\n      message.error('切换AI提供商失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 发送聊天消息\n  const sendMessage = async () => {\n    if (!inputMessage.trim()) return;\n    const userMessage = {\n      role: 'user',\n      content: inputMessage\n    };\n    const newMessages = [...messages, userMessage];\n    setMessages(newMessages);\n    setInputMessage('');\n    setLoading(true);\n    try {\n      const response = await axios.post('/api/v1/ai/chat', {\n        messages: newMessages\n      });\n      const aiMessage = {\n        role: 'assistant',\n        content: response.data.response\n      };\n      setMessages([...newMessages, aiMessage]);\n    } catch (error) {\n      message.error('AI对话失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 生成内容\n  const generateContent = async (type, values, isBatch = false) => {\n    try {\n      setLoading(true);\n      const params = {\n        prompt: values.prompt,\n        max_tokens: values.maxTokens || aiParams.max_tokens,\n        temperature: values.temperature || aiParams.temperature,\n        ...aiParams\n      };\n      if (isBatch && batchMode) {\n        // 批量生成\n        const results = [];\n        for (let i = 0; i < batchCount; i++) {\n          const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n          results.push({\n            id: Date.now() + i,\n            content: response.data.content,\n            type: type,\n            timestamp: new Date().toLocaleString(),\n            provider: currentProvider\n          });\n        }\n\n        // 保存到历史记录\n        setContentHistory(prev => [...results, ...prev]);\n        setGeneratedContent(results[0].content);\n        message.success(`批量生成${batchCount}个${type}成功`);\n        return results[0].content;\n      } else {\n        // 单个生成\n        const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n        const result = {\n          id: Date.now(),\n          content: response.data.content,\n          type: type,\n          timestamp: new Date().toLocaleString(),\n          provider: currentProvider\n        };\n\n        // 保存到历史记录\n        setContentHistory(prev => [result, ...prev]);\n        setGeneratedContent(response.data.content);\n        message.success('内容生成成功');\n        return response.data.content;\n      }\n    } catch (error) {\n      message.error(`生成${type}失败`);\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存内容到本地\n  const saveContent = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/plain;charset=utf-8'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename || `ai_content_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n    message.success('内容已保存到本地');\n  };\n\n  // 复制内容到剪贴板\n  const copyToClipboard = content => {\n    navigator.clipboard.writeText(content).then(() => {\n      message.success('内容已复制到剪贴板');\n    }).catch(() => {\n      message.error('复制失败');\n    });\n  };\n\n  // 加载模板\n  const loadTemplates = () => {\n    const defaultTemplates = [{\n      id: 1,\n      name: '玄幻世界设定',\n      type: 'setting',\n      content: '创建一个修仙世界，包含多个门派，有完整的修炼体系和等级划分...'\n    }, {\n      id: 2,\n      name: '现代都市背景',\n      type: 'setting',\n      content: '设定一个现代都市背景，包含商业、科技、社会结构...'\n    }, {\n      id: 3,\n      name: '主角人物模板',\n      type: 'character',\n      content: '创建一个年轻的主角，有特殊能力，性格坚韧不拔...'\n    }, {\n      id: 4,\n      name: '反派角色模板',\n      type: 'character',\n      content: '设计一个有深度的反派角色，有合理的动机和背景...'\n    }];\n    setTemplates(defaultTemplates);\n  };\n\n  // 应用模板\n  const applyTemplate = template => {\n    if (template.type === 'setting') {\n      generateForm.setFieldsValue({\n        prompt: template.content\n      });\n      setActiveTab('setting');\n    } else if (template.type === 'character') {\n      generateForm.setFieldsValue({\n        prompt: template.content\n      });\n      setActiveTab('character');\n    }\n    setShowTemplates(false);\n    message.success(`已应用模板：${template.name}`);\n  };\n\n  // 滚动到消息底部\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    fetchProviders();\n    fetchAIStatus();\n    loadTemplates();\n  }, []);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // 状态指示器\n  const StatusIndicator = () => /*#__PURE__*/_jsxDEV(Space, {\n    children: [/*#__PURE__*/_jsxDEV(Tag, {\n      color: aiStatus.connected ? 'green' : 'red',\n      children: aiStatus.connected ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: [\"\\u5F53\\u524D\\u63D0\\u4F9B\\u5546: \", currentProvider]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 15\n      }, this),\n      onClick: fetchAIStatus,\n      loading: loading,\n      children: \"\\u5237\\u65B0\\u72B6\\u6001\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowHistory(true),\n      children: [\"\\u5386\\u53F2\\u8BB0\\u5F55 \", /*#__PURE__*/_jsxDEV(Badge, {\n        count: contentHistory.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 14\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowTemplates(true),\n      children: \"\\u6A21\\u677F\\u5E93\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowSettings(true),\n      children: \"\\u9AD8\\u7EA7\\u8BBE\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n\n  // AI参数设置面板\n  const AISettingsPanel = () => /*#__PURE__*/_jsxDEV(Drawer, {\n    title: \"AI\\u9AD8\\u7EA7\\u53C2\\u6570\\u8BBE\\u7F6E\",\n    placement: \"right\",\n    onClose: () => setShowSettings(false),\n    open: showSettings,\n    width: 400,\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      layout: \"vertical\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6E29\\u5EA6\\u53C2\\u6570 (Temperature)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: 0,\n          max: 2,\n          step: 0.1,\n          value: aiParams.temperature,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            temperature: value\n          })),\n          marks: {\n            0: '保守',\n            0.7: '平衡',\n            1.4: '创新',\n            2: '随机'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u8F93\\u51FA\\u7684\\u968F\\u673A\\u6027\\u548C\\u521B\\u9020\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6700\\u5927Token\\u6570\",\n        children: [/*#__PURE__*/_jsxDEV(InputNumber, {\n          min: 100,\n          max: 8000,\n          value: aiParams.max_tokens,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            max_tokens: value\n          })),\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u751F\\u6210\\u5185\\u5BB9\\u7684\\u957F\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"Top P\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: 0,\n          max: 1,\n          step: 0.1,\n          value: aiParams.top_p,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            top_p: value\n          })),\n          marks: {\n            0: '0',\n            0.5: '0.5',\n            1: '1'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u63A7\\u5236\\u8BCD\\u6C47\\u9009\\u62E9\\u7684\\u591A\\u6837\\u6027\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u9891\\u7387\\u60E9\\u7F5A (Frequency Penalty)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: -2,\n          max: 2,\n          step: 0.1,\n          value: aiParams.frequency_penalty,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            frequency_penalty: value\n          })),\n          marks: {\n            '-2': '-2',\n            '0': '0',\n            '2': '2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u51CF\\u5C11\\u91CD\\u590D\\u5185\\u5BB9\\u7684\\u51FA\\u73B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u5B58\\u5728\\u60E9\\u7F5A (Presence Penalty)\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          min: -2,\n          max: 2,\n          step: 0.1,\n          value: aiParams.presence_penalty,\n          onChange: value => setAiParams(prev => ({\n            ...prev,\n            presence_penalty: value\n          })),\n          marks: {\n            '-2': '-2',\n            '0': '0',\n            '2': '2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u9F13\\u52B1\\u8BA8\\u8BBA\\u65B0\\u8BDD\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        label: \"\\u6279\\u91CF\\u751F\\u6210\\u6A21\\u5F0F\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Switch, {\n            checked: batchMode,\n            onChange: setBatchMode,\n            checkedChildren: \"\\u5F00\\u542F\",\n            unCheckedChildren: \"\\u5173\\u95ED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), batchMode && /*#__PURE__*/_jsxDEV(InputNumber, {\n            min: 2,\n            max: 10,\n            value: batchCount,\n            onChange: setBatchCount,\n            addonBefore: \"\\u751F\\u6210\\u6570\\u91CF\",\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          block: true,\n          onClick: () => {\n            message.success('参数设置已保存');\n            setShowSettings(false);\n          },\n          children: \"\\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n\n  // 历史记录面板\n  const HistoryPanel = () => /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"\\u751F\\u6210\\u5386\\u53F2\\u8BB0\\u5F55\",\n    open: showHistory,\n    onCancel: () => setShowHistory(false),\n    width: 800,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      danger: true,\n      onClick: () => {\n        setContentHistory([]);\n        message.success('历史记录已清空');\n      },\n      children: \"\\u6E05\\u7A7A\\u8BB0\\u5F55\"\n    }, \"clear\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setShowHistory(false),\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: contentHistory,\n      renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n        actions: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 23\n          }, this),\n          onClick: () => copyToClipboard(item.content),\n          children: \"\\u590D\\u5236\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 23\n          }, this),\n          onClick: () => saveContent(item.content, `${item.type}_${item.id}.txt`),\n          children: \"\\u4E0B\\u8F7D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\",\n          onConfirm: () => {\n            setContentHistory(prev => prev.filter(h => h.id !== item.id));\n            message.success('记录已删除');\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 51\n            }, this),\n            children: \"\\u5220\\u9664\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 15\n        }, this)],\n        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: item.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: item.provider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(Paragraph, {\n            ellipsis: {\n              rows: 3,\n              expandable: true\n            },\n            style: {\n              marginBottom: 0\n            },\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 11\n      }, this),\n      locale: {\n        emptyText: '暂无生成记录'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 454,\n    columnNumber: 5\n  }, this);\n\n  // 模板库面板\n  const TemplatePanel = () => /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"\\u6A21\\u677F\\u5E93\",\n    open: showTemplates,\n    onCancel: () => setShowTemplates(false),\n    width: 600,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setShowTemplates(false),\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this)],\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: templates,\n      renderItem: template => /*#__PURE__*/_jsxDEV(List.Item, {\n        actions: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: () => applyTemplate(template),\n          children: \"\\u5E94\\u7528\\u6A21\\u677F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 15\n        }, this)],\n        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: template.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: template.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 17\n          }, this),\n          description: /*#__PURE__*/_jsxDEV(Paragraph, {\n            ellipsis: {\n              rows: 2,\n              expandable: true\n            },\n            style: {\n              marginBottom: 0\n            },\n            children: template.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 529,\n    columnNumber: 5\n  }, this);\n\n  // 聊天界面\n  const ChatInterface = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '600px',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        padding: '16px',\n        border: '1px solid #d9d9d9',\n        borderRadius: '6px'\n      },\n      children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          color: '#999',\n          marginTop: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {\n          style: {\n            fontSize: '48px',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F00\\u59CB\\u4E0EAI\\u52A9\\u624B\\u5BF9\\u8BDD\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this) : messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'flex-start'\n          },\n          children: [msg.role === 'user' ? /*#__PURE__*/_jsxDEV(UserOutlined, {\n            style: {\n              marginRight: '8px',\n              marginTop: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(RobotOutlined, {\n            style: {\n              marginRight: '8px',\n              marginTop: '4px',\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: msg.role === 'user' ? '用户' : 'AI助手'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginTop: '4px',\n                marginBottom: 0\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 13\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"AI\\u6B63\\u5728\\u601D\\u8003\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 22\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input.Group, {\n        compact: true,\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          style: {\n            width: 'calc(100% - 80px)'\n          },\n          placeholder: \"\\u8F93\\u5165\\u60A8\\u7684\\u95EE\\u9898...\",\n          value: inputMessage,\n          onChange: e => setInputMessage(e.target.value),\n          onPressEnter: sendMessage,\n          disabled: loading || !aiStatus.connected\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 19\n          }, this),\n          onClick: sendMessage,\n          loading: loading,\n          disabled: !aiStatus.connected,\n          children: \"\\u53D1\\u9001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 578,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"AI\\u52A9\\u624B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 18,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: activeTab,\n            onChange: setActiveTab,\n            children: [/*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 35\n                }, this), \"\\u667A\\u80FD\\u5BF9\\u8BDD\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ChatInterface, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this)\n            }, \"chat\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 35\n                }, this), \"\\u8BBE\\u5B9A\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: generateForm,\n                layout: \"vertical\",\n                onFinish: values => generateContent('setting', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u751F\\u6210\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入生成要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u4E16\\u754C\\u8BBE\\u5B9A\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E00\\u4E2A\\u4FEE\\u4ED9\\u4E16\\u754C\\uFF0C\\u6709\\u591A\\u4E2A\\u95E8\\u6D3E...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"1000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 672,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 673,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 674,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.3,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 681,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 680,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 696,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 696,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成世界设定'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 703,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 709,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'setting.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this)\n            }, \"setting\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 35\n                }, this), \"\\u4EBA\\u7269\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                layout: \"vertical\",\n                onFinish: values => generateContent('character', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u4EBA\\u7269\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入人物要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u4EBA\\u7269\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E00\\u4E2A\\u5E74\\u8F7B\\u7684\\u5251\\u4FEE\\uFF0C\\u6027\\u683C\\u51B7\\u50B2...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"1000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 752,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 753,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 750,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.3,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 762,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 763,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 759,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成人物设定'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 782,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'character.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this)\n            }, \"character\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 35\n                }, this), \"\\u5267\\u60C5\\u751F\\u6210\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: plotForm,\n                layout: \"vertical\",\n                onFinish: values => generateContent('plot', values, batchMode),\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u5267\\u60C5\\u8981\\u6C42\",\n                  rules: [{\n                    required: true,\n                    message: '请输入剧情要求'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u60A8\\u60F3\\u8981\\u751F\\u6210\\u7684\\u5267\\u60C5\\uFF0C\\u4F8B\\u5982\\uFF1A\\u4E3B\\u89D2\\u5728\\u4FEE\\u4ED9\\u95E8\\u6D3E\\u4E2D\\u9047\\u5230\\u7684\\u7B2C\\u4E00\\u4E2A\\u6311\\u6218...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u6700\\u5927\\u5B57\\u6570\",\n                      initialValue: 3000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"2000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 833,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"3000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 5000,\n                          children: \"5000\\u5B57\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 832,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.8,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.5,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 842,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.8,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 843,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 1.0,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 844,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 841,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 840,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"plotType\",\n                      label: \"\\u5267\\u60C5\\u7C7B\\u578B\",\n                      initialValue: \"adventure\",\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: \"adventure\",\n                          children: \"\\u5192\\u9669\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 851,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"romance\",\n                          children: \"\\u7231\\u60C5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"conflict\",\n                          children: \"\\u51B2\\u7A81\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 853,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: \"mystery\",\n                          children: \"\\u60AC\\u7591\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 854,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 850,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: batchMode ? /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 69\n                      }, this),\n                      children: batchMode ? `批量生成(${batchCount}个)` : '生成剧情大纲'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 874,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 873,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 880,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'plot.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 879,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u751F\\u6210\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this)\n            }, \"plot\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 35\n                }, this), \"\\u7EED\\u5199\\u529F\\u80FD\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: continueForm,\n                layout: \"vertical\",\n                onFinish: values => {\n                  const prompt = values.continueHint ? `${values.prompt}\\n\\n续写提示：${values.continueHint}` : values.prompt;\n                  generateContent('continue-writing', {\n                    ...values,\n                    prompt\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u539F\\u6587\\u5185\\u5BB9\",\n                  rules: [{\n                    required: true,\n                    message: '请输入需要续写的原文内容'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 6,\n                    placeholder: \"\\u8BF7\\u7C98\\u8D34\\u9700\\u8981\\u7EED\\u5199\\u7684\\u539F\\u6587\\u5185\\u5BB9...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"continueHint\",\n                  label: \"\\u7EED\\u5199\\u63D0\\u793A\\uFF08\\u53EF\\u9009\\uFF09\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 2,\n                    placeholder: \"\\u53EF\\u4EE5\\u63D0\\u4F9B\\u7EED\\u5199\\u7684\\u65B9\\u5411\\u63D0\\u793A\\uFF0C\\u4F8B\\u5982\\uFF1A\\u63A5\\u4E0B\\u6765\\u4E3B\\u89D2\\u9047\\u5230\\u4E86...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"maxTokens\",\n                      label: \"\\u7EED\\u5199\\u957F\\u5EA6\",\n                      initialValue: 2000,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 1000,\n                          children: \"\\u77ED\\u7BC7(1000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 939,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 2000,\n                          children: \"\\u4E2D\\u7BC7(2000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 3000,\n                          children: \"\\u957F\\u7BC7(3000\\u5B57)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 941,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 938,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 937,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"temperature\",\n                      label: \"\\u521B\\u610F\\u5EA6\",\n                      initialValue: 0.7,\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        children: [/*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.5,\n                          children: \"\\u4FDD\\u5B88\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 948,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.7,\n                          children: \"\\u5E73\\u8861\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 949,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Option, {\n                          value: 0.9,\n                          children: \"\\u521B\\u65B0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 950,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 947,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 31\n                      }, this),\n                      children: \"AI\\u7EED\\u5199\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 970,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 969,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 976,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'continue.txt'),\n                        children: \"\\u4E0B\\u8F7D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 975,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u7EED\\u5199\\u7ED3\\u679C\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 17\n              }, this)\n            }, \"continue\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 35\n                }, this), \"\\u4E00\\u81F4\\u6027\\u68C0\\u67E5\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 29\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: checkForm,\n                layout: \"vertical\",\n                onFinish: values => {\n                  const prompt = `检查类型：${values.checkType}\\n\\n内容：\\n${values.prompt}`;\n                  generateContent('check-consistency', {\n                    ...values,\n                    prompt\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"prompt\",\n                  label: \"\\u68C0\\u67E5\\u5185\\u5BB9\",\n                  rules: [{\n                    required: true,\n                    message: '请输入需要检查的内容'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 8,\n                    placeholder: \"\\u8BF7\\u7C98\\u8D34\\u9700\\u8981\\u8FDB\\u884C\\u4E00\\u81F4\\u6027\\u68C0\\u67E5\\u7684\\u5C0F\\u8BF4\\u5185\\u5BB9...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"checkType\",\n                  label: \"\\u68C0\\u67E5\\u7C7B\\u578B\",\n                  initialValue: \"all\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"all\",\n                      children: \"\\u5168\\u9762\\u68C0\\u67E5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"character\",\n                      children: \"\\u4EBA\\u7269\\u4E00\\u81F4\\u6027\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"plot\",\n                      children: \"\\u60C5\\u8282\\u903B\\u8F91\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"setting\",\n                      children: \"\\u8BBE\\u5B9A\\u4E00\\u81F4\\u6027\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"timeline\",\n                      children: \"\\u65F6\\u95F4\\u7EBF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      loading: loading,\n                      disabled: !aiStatus.connected,\n                      icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1040,\n                        columnNumber: 31\n                      }, this),\n                      children: \"\\u5F00\\u59CB\\u68C0\\u67E5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 23\n                    }, this), generatedContent && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1047,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => copyToClipboard(generatedContent),\n                        children: \"\\u590D\\u5236\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1053,\n                          columnNumber: 35\n                        }, this),\n                        onClick: () => saveContent(generatedContent, 'check_report.txt'),\n                        children: \"\\u4E0B\\u8F7D\\u62A5\\u544A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1052,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 19\n                }, this), generatedContent && /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u68C0\\u67E5\\u62A5\\u544A\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    value: generatedContent,\n                    rows: 8,\n                    readOnly: true,\n                    style: {\n                      backgroundColor: '#f5f5f5'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1064,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 17\n              }, this)\n            }, \"check\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"AI\\u8BBE\\u7F6E\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"AI\\u63D0\\u4F9B\\u5546\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              style: {\n                width: '100%',\n                marginTop: '8px'\n              },\n              value: currentProvider,\n              onChange: switchProvider,\n              loading: loading,\n              children: providers.map(provider => /*#__PURE__*/_jsxDEV(Option, {\n                value: provider,\n                children: provider.toUpperCase()\n              }, provider, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1097,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5FEB\\u901F\\u64CD\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                style: {\n                  marginBottom: '8px'\n                },\n                onClick: () => setMessages([]),\n                children: \"\\u6E05\\u7A7A\\u5BF9\\u8BDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                block: true,\n                onClick: fetchAIStatus,\n                loading: loading,\n                children: \"\\u68C0\\u67E5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1099,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this), !aiStatus.connected && /*#__PURE__*/_jsxDEV(Alert, {\n          style: {\n            marginTop: '16px'\n          },\n          message: \"AI\\u670D\\u52A1\\u79BB\\u7EBF\",\n          description: \"\\u8BF7\\u68C0\\u67E5AI\\u670D\\u52A1\\u914D\\u7F6E\\u6216\\u7F51\\u7EDC\\u8FDE\\u63A5\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AISettingsPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HistoryPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TemplatePanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 637,\n    columnNumber: 5\n  }, this);\n};\n_s(AIAssistant, \"nSfH8zGHU+A9/Ej0sfxo07wrDsI=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = AIAssistant;\nexport default AIAssistant;\nvar _c;\n$RefreshReg$(_c, \"AIAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "Typography", "<PERSON><PERSON>", "Input", "Select", "Row", "Col", "Tabs", "Form", "message", "Spin", "Space", "Tag", "Divider", "<PERSON><PERSON>", "Modal", "List", "Slide<PERSON>", "Switch", "InputNumber", "Badge", "Popconfirm", "Drawer", "RobotOutlined", "MessageOutlined", "SendOutlined", "BulbOutlined", "UserOutlined", "ReloadOutlined", "BookOutlined", "EditOutlined", "CheckCircleOutlined", "HistoryOutlined", "DownloadOutlined", "CopyOutlined", "DeleteOutlined", "SettingOutlined", "FileTextOutlined", "ThunderboltOutlined", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "TextArea", "Option", "TabPane", "AIAssistant", "_s", "loading", "setLoading", "providers", "setProviders", "currentProvider", "setCurrentProvider", "aiStatus", "setAiStatus", "connected", "status", "messages", "setMessages", "inputMessage", "setInputMessage", "generateForm", "useForm", "plotForm", "continueForm", "checkForm", "activeTab", "setActiveTab", "messagesEndRef", "generatedContent", "setGeneratedContent", "contentHistory", "setContentHistory", "showHistory", "setShowHistory", "showTemplates", "setShowTemplates", "showSettings", "setShowSettings", "templates", "setTemplates", "aiParams", "setAiParams", "temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "batchMode", "setBatchMode", "batchCount", "setBatchCount", "fetchProviders", "response", "get", "data", "current", "error", "fetchAIStatus", "switchProvider", "provider", "post", "success", "sendMessage", "trim", "userMessage", "role", "content", "newMessages", "aiMessage", "generateContent", "type", "values", "isBatch", "params", "prompt", "maxTokens", "results", "i", "push", "id", "Date", "now", "timestamp", "toLocaleString", "prev", "result", "saveContent", "filename", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "copyToClipboard", "navigator", "clipboard", "writeText", "then", "catch", "loadTemplates", "defaultTemplates", "name", "applyTemplate", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollToBottom", "_messagesEndRef$curre", "scrollIntoView", "behavior", "StatusIndicator", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "icon", "onClick", "count", "length", "AISettingsPanel", "title", "placement", "onClose", "open", "width", "layout", "<PERSON><PERSON>", "label", "min", "max", "step", "value", "onChange", "marks", "style", "direction", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "addonBefore", "block", "HistoryPanel", "onCancel", "footer", "danger", "dataSource", "renderItem", "item", "actions", "onConfirm", "filter", "h", "Meta", "description", "ellipsis", "rows", "expandable", "marginBottom", "locale", "emptyText", "TemplatePanel", "strong", "ChatInterface", "height", "display", "flexDirection", "flex", "overflowY", "padding", "border", "borderRadius", "textAlign", "marginTop", "fontSize", "map", "msg", "index", "alignItems", "marginRight", "ref", "Group", "compact", "placeholder", "e", "target", "onPressEnter", "disabled", "className", "level", "gutter", "span", "active<PERSON><PERSON>", "tab", "form", "onFinish", "rules", "required", "initialValue", "htmlType", "readOnly", "backgroundColor", "continueHint", "checkType", "toUpperCase", "showIcon", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/AIAssistant.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tabs,\n  Form,\n  message,\n  Spin,\n  Space,\n  Tag,\n  Divider,\n  Alert,\n  Modal,\n  List,\n\n  Slider,\n  Switch,\n  Input<PERSON><PERSON>ber,\n\n  Badge,\n  Popconfirm,\n\n  Drawer\n} from 'antd';\nimport {\n  RobotOutlined,\n  MessageOutlined,\n  SendOutlined,\n  BulbOutlined,\n  UserOutlined,\n  ReloadOutlined,\n  BookOutlined,\n  EditOutlined,\n  CheckCircleOutlined,\n\n  HistoryOutlined,\n\n  DownloadOutlined,\n\n  CopyOutlined,\n  DeleteOutlined,\n  SettingOutlined,\n\n  FileTextOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\n\nconst AIAssistant = () => {\n  const [loading, setLoading] = useState(false);\n  const [providers, setProviders] = useState([]);\n  const [currentProvider, setCurrentProvider] = useState('');\n  const [aiStatus, setAiStatus] = useState({ connected: false, status: 'offline' });\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [generateForm] = Form.useForm();\n  const [plotForm] = Form.useForm();\n  const [continueForm] = Form.useForm();\n  const [checkForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('chat');\n  const messagesEndRef = useRef(null);\n\n  // 新增状态\n  const [generatedContent, setGeneratedContent] = useState('');\n  const [contentHistory, setContentHistory] = useState([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const [showTemplates, setShowTemplates] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [templates, setTemplates] = useState([]);\n  const [aiParams, setAiParams] = useState({\n    temperature: 0.7,\n    max_tokens: 2000,\n    top_p: 1.0,\n    frequency_penalty: 0.0,\n    presence_penalty: 0.0\n  });\n  const [batchMode, setBatchMode] = useState(false);\n  const [batchCount, setBatchCount] = useState(3);\n\n  // 获取AI提供商列表\n  const fetchProviders = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/providers');\n      setProviders(response.data.providers);\n      setCurrentProvider(response.data.current);\n    } catch (error) {\n      message.error('获取AI提供商列表失败');\n    }\n  };\n\n  // 获取AI状态\n  const fetchAIStatus = async () => {\n    try {\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n    } catch (error) {\n      setAiStatus({ connected: false, status: 'error' });\n    }\n  };\n\n  // 切换AI提供商\n  const switchProvider = async (provider) => {\n    try {\n      setLoading(true);\n      await axios.post('/api/v1/ai/switch-provider', { provider });\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${provider}`);\n      await fetchAIStatus();\n    } catch (error) {\n      message.error('切换AI提供商失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 发送聊天消息\n  const sendMessage = async () => {\n    if (!inputMessage.trim()) return;\n\n    const userMessage = { role: 'user', content: inputMessage };\n    const newMessages = [...messages, userMessage];\n    setMessages(newMessages);\n    setInputMessage('');\n    setLoading(true);\n\n    try {\n      const response = await axios.post('/api/v1/ai/chat', {\n        messages: newMessages\n      });\n\n      const aiMessage = { role: 'assistant', content: response.data.response };\n      setMessages([...newMessages, aiMessage]);\n    } catch (error) {\n      message.error('AI对话失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 生成内容\n  const generateContent = async (type, values, isBatch = false) => {\n    try {\n      setLoading(true);\n      const params = {\n        prompt: values.prompt,\n        max_tokens: values.maxTokens || aiParams.max_tokens,\n        temperature: values.temperature || aiParams.temperature,\n        ...aiParams\n      };\n\n      if (isBatch && batchMode) {\n        // 批量生成\n        const results = [];\n        for (let i = 0; i < batchCount; i++) {\n          const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n          results.push({\n            id: Date.now() + i,\n            content: response.data.content,\n            type: type,\n            timestamp: new Date().toLocaleString(),\n            provider: currentProvider\n          });\n        }\n\n        // 保存到历史记录\n        setContentHistory(prev => [...results, ...prev]);\n        setGeneratedContent(results[0].content);\n        message.success(`批量生成${batchCount}个${type}成功`);\n        return results[0].content;\n      } else {\n        // 单个生成\n        const response = await axios.post(`/api/v1/ai/generate-${type}`, params);\n        const result = {\n          id: Date.now(),\n          content: response.data.content,\n          type: type,\n          timestamp: new Date().toLocaleString(),\n          provider: currentProvider\n        };\n\n        // 保存到历史记录\n        setContentHistory(prev => [result, ...prev]);\n        setGeneratedContent(response.data.content);\n        message.success('内容生成成功');\n        return response.data.content;\n      }\n    } catch (error) {\n      message.error(`生成${type}失败`);\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存内容到本地\n  const saveContent = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename || `ai_content_${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n    message.success('内容已保存到本地');\n  };\n\n  // 复制内容到剪贴板\n  const copyToClipboard = (content) => {\n    navigator.clipboard.writeText(content).then(() => {\n      message.success('内容已复制到剪贴板');\n    }).catch(() => {\n      message.error('复制失败');\n    });\n  };\n\n  // 加载模板\n  const loadTemplates = () => {\n    const defaultTemplates = [\n      {\n        id: 1,\n        name: '玄幻世界设定',\n        type: 'setting',\n        content: '创建一个修仙世界，包含多个门派，有完整的修炼体系和等级划分...'\n      },\n      {\n        id: 2,\n        name: '现代都市背景',\n        type: 'setting',\n        content: '设定一个现代都市背景，包含商业、科技、社会结构...'\n      },\n      {\n        id: 3,\n        name: '主角人物模板',\n        type: 'character',\n        content: '创建一个年轻的主角，有特殊能力，性格坚韧不拔...'\n      },\n      {\n        id: 4,\n        name: '反派角色模板',\n        type: 'character',\n        content: '设计一个有深度的反派角色，有合理的动机和背景...'\n      }\n    ];\n    setTemplates(defaultTemplates);\n  };\n\n  // 应用模板\n  const applyTemplate = (template) => {\n    if (template.type === 'setting') {\n      generateForm.setFieldsValue({ prompt: template.content });\n      setActiveTab('setting');\n    } else if (template.type === 'character') {\n      generateForm.setFieldsValue({ prompt: template.content });\n      setActiveTab('character');\n    }\n    setShowTemplates(false);\n    message.success(`已应用模板：${template.name}`);\n  };\n\n  // 滚动到消息底部\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    fetchProviders();\n    fetchAIStatus();\n    loadTemplates();\n  }, []);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // 状态指示器\n  const StatusIndicator = () => (\n    <Space>\n      <Tag color={aiStatus.connected ? 'green' : 'red'}>\n        {aiStatus.connected ? '在线' : '离线'}\n      </Tag>\n      <Text type=\"secondary\">当前提供商: {currentProvider}</Text>\n      <Button\n        size=\"small\"\n        icon={<ReloadOutlined />}\n        onClick={fetchAIStatus}\n        loading={loading}\n      >\n        刷新状态\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<HistoryOutlined />}\n        onClick={() => setShowHistory(true)}\n      >\n        历史记录 <Badge count={contentHistory.length} />\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<FileTextOutlined />}\n        onClick={() => setShowTemplates(true)}\n      >\n        模板库\n      </Button>\n      <Button\n        size=\"small\"\n        icon={<SettingOutlined />}\n        onClick={() => setShowSettings(true)}\n      >\n        高级设置\n      </Button>\n    </Space>\n  );\n\n  // AI参数设置面板\n  const AISettingsPanel = () => (\n    <Drawer\n      title=\"AI高级参数设置\"\n      placement=\"right\"\n      onClose={() => setShowSettings(false)}\n      open={showSettings}\n      width={400}\n    >\n      <Form layout=\"vertical\">\n        <Form.Item label=\"温度参数 (Temperature)\">\n          <Slider\n            min={0}\n            max={2}\n            step={0.1}\n            value={aiParams.temperature}\n            onChange={(value) => setAiParams(prev => ({ ...prev, temperature: value }))}\n            marks={{\n              0: '保守',\n              0.7: '平衡',\n              1.4: '创新',\n              2: '随机'\n            }}\n          />\n          <Text type=\"secondary\">控制输出的随机性和创造性</Text>\n        </Form.Item>\n\n        <Form.Item label=\"最大Token数\">\n          <InputNumber\n            min={100}\n            max={8000}\n            value={aiParams.max_tokens}\n            onChange={(value) => setAiParams(prev => ({ ...prev, max_tokens: value }))}\n            style={{ width: '100%' }}\n          />\n          <Text type=\"secondary\">控制生成内容的长度</Text>\n        </Form.Item>\n\n        <Form.Item label=\"Top P\">\n          <Slider\n            min={0}\n            max={1}\n            step={0.1}\n            value={aiParams.top_p}\n            onChange={(value) => setAiParams(prev => ({ ...prev, top_p: value }))}\n            marks={{\n              0: '0',\n              0.5: '0.5',\n              1: '1'\n            }}\n          />\n          <Text type=\"secondary\">控制词汇选择的多样性</Text>\n        </Form.Item>\n\n        <Form.Item label=\"频率惩罚 (Frequency Penalty)\">\n          <Slider\n            min={-2}\n            max={2}\n            step={0.1}\n            value={aiParams.frequency_penalty}\n            onChange={(value) => setAiParams(prev => ({ ...prev, frequency_penalty: value }))}\n            marks={{\n              '-2': '-2',\n              '0': '0',\n              '2': '2'\n            }}\n          />\n          <Text type=\"secondary\">减少重复内容的出现</Text>\n        </Form.Item>\n\n        <Form.Item label=\"存在惩罚 (Presence Penalty)\">\n          <Slider\n            min={-2}\n            max={2}\n            step={0.1}\n            value={aiParams.presence_penalty}\n            onChange={(value) => setAiParams(prev => ({ ...prev, presence_penalty: value }))}\n            marks={{\n              '-2': '-2',\n              '0': '0',\n              '2': '2'\n            }}\n          />\n          <Text type=\"secondary\">鼓励讨论新话题</Text>\n        </Form.Item>\n\n        <Divider />\n\n        <Form.Item label=\"批量生成模式\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <Switch\n              checked={batchMode}\n              onChange={setBatchMode}\n              checkedChildren=\"开启\"\n              unCheckedChildren=\"关闭\"\n            />\n            {batchMode && (\n              <InputNumber\n                min={2}\n                max={10}\n                value={batchCount}\n                onChange={setBatchCount}\n                addonBefore=\"生成数量\"\n                style={{ width: '100%' }}\n              />\n            )}\n          </Space>\n        </Form.Item>\n\n        <Form.Item>\n          <Button\n            type=\"primary\"\n            block\n            onClick={() => {\n              message.success('参数设置已保存');\n              setShowSettings(false);\n            }}\n          >\n            保存设置\n          </Button>\n        </Form.Item>\n      </Form>\n    </Drawer>\n  );\n\n  // 历史记录面板\n  const HistoryPanel = () => (\n    <Modal\n      title=\"生成历史记录\"\n      open={showHistory}\n      onCancel={() => setShowHistory(false)}\n      width={800}\n      footer={[\n        <Button key=\"clear\" danger onClick={() => {\n          setContentHistory([]);\n          message.success('历史记录已清空');\n        }}>\n          清空记录\n        </Button>,\n        <Button key=\"close\" onClick={() => setShowHistory(false)}>\n          关闭\n        </Button>\n      ]}\n    >\n      <List\n        dataSource={contentHistory}\n        renderItem={(item) => (\n          <List.Item\n            actions={[\n              <Button\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(item.content)}\n              >\n                复制\n              </Button>,\n              <Button\n                size=\"small\"\n                icon={<DownloadOutlined />}\n                onClick={() => saveContent(item.content, `${item.type}_${item.id}.txt`)}\n              >\n                下载\n              </Button>,\n              <Popconfirm\n                title=\"确定删除这条记录吗？\"\n                onConfirm={() => {\n                  setContentHistory(prev => prev.filter(h => h.id !== item.id));\n                  message.success('记录已删除');\n                }}\n              >\n                <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                  删除\n                </Button>\n              </Popconfirm>\n            ]}\n          >\n            <List.Item.Meta\n              title={\n                <Space>\n                  <Tag color=\"blue\">{item.type}</Tag>\n                  <Text>{item.timestamp}</Text>\n                  <Tag color=\"green\">{item.provider}</Tag>\n                </Space>\n              }\n              description={\n                <Paragraph\n                  ellipsis={{ rows: 3, expandable: true }}\n                  style={{ marginBottom: 0 }}\n                >\n                  {item.content}\n                </Paragraph>\n              }\n            />\n          </List.Item>\n        )}\n        locale={{ emptyText: '暂无生成记录' }}\n      />\n    </Modal>\n  );\n\n  // 模板库面板\n  const TemplatePanel = () => (\n    <Modal\n      title=\"模板库\"\n      open={showTemplates}\n      onCancel={() => setShowTemplates(false)}\n      width={600}\n      footer={[\n        <Button key=\"close\" onClick={() => setShowTemplates(false)}>\n          关闭\n        </Button>\n      ]}\n    >\n      <List\n        dataSource={templates}\n        renderItem={(template) => (\n          <List.Item\n            actions={[\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                onClick={() => applyTemplate(template)}\n              >\n                应用模板\n              </Button>\n            ]}\n          >\n            <List.Item.Meta\n              title={\n                <Space>\n                  <Text strong>{template.name}</Text>\n                  <Tag color=\"blue\">{template.type}</Tag>\n                </Space>\n              }\n              description={\n                <Paragraph\n                  ellipsis={{ rows: 2, expandable: true }}\n                  style={{ marginBottom: 0 }}\n                >\n                  {template.content}\n                </Paragraph>\n              }\n            />\n          </List.Item>\n        )}\n      />\n    </Modal>\n  );\n\n  // 聊天界面\n  const ChatInterface = () => (\n    <div style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>\n      <div style={{ flex: 1, overflowY: 'auto', padding: '16px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>\n        {messages.length === 0 ? (\n          <div style={{ textAlign: 'center', color: '#999', marginTop: '100px' }}>\n            <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />\n            <div>开始与AI助手对话吧！</div>\n          </div>\n        ) : (\n          messages.map((msg, index) => (\n            <div key={index} style={{ marginBottom: '16px' }}>\n              <div style={{ display: 'flex', alignItems: 'flex-start' }}>\n                {msg.role === 'user' ? (\n                  <UserOutlined style={{ marginRight: '8px', marginTop: '4px' }} />\n                ) : (\n                  <RobotOutlined style={{ marginRight: '8px', marginTop: '4px', color: '#1890ff' }} />\n                )}\n                <div style={{ flex: 1 }}>\n                  <Text strong>{msg.role === 'user' ? '用户' : 'AI助手'}</Text>\n                  <Paragraph style={{ marginTop: '4px', marginBottom: 0 }}>\n                    {msg.content}\n                  </Paragraph>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n        {loading && (\n          <div style={{ textAlign: 'center', padding: '16px' }}>\n            <Spin /> <Text type=\"secondary\">AI正在思考中...</Text>\n          </div>\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      <div style={{ marginTop: '16px' }}>\n        <Input.Group compact>\n          <Input\n            style={{ width: 'calc(100% - 80px)' }}\n            placeholder=\"输入您的问题...\"\n            value={inputMessage}\n            onChange={(e) => setInputMessage(e.target.value)}\n            onPressEnter={sendMessage}\n            disabled={loading || !aiStatus.connected}\n          />\n          <Button\n            type=\"primary\"\n            icon={<SendOutlined />}\n            onClick={sendMessage}\n            loading={loading}\n            disabled={!aiStatus.connected}\n          >\n            发送\n          </Button>\n        </Input.Group>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">AI助手</Title>\n        <StatusIndicator />\n      </div>\n\n      <Row gutter={[16, 16]}>\n        <Col span={18}>\n          <Card>\n            <Tabs activeKey={activeTab} onChange={setActiveTab}>\n              <TabPane tab={<span><MessageOutlined />智能对话</span>} key=\"chat\">\n                <ChatInterface />\n              </TabPane>\n\n              <TabPane tab={<span><BulbOutlined />设定生成</span>} key=\"setting\">\n                <Form\n                  form={generateForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('setting', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"生成要求\"\n                    rules={[{ required: true, message: '请输入生成要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的世界设定，例如：一个修仙世界，有多个门派...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>1000字</Option>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.3}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <BulbOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成世界设定'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'setting.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><UserOutlined />人物生成</span>} key=\"character\">\n                <Form\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('character', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"人物要求\"\n                    rules={[{ required: true, message: '请输入人物要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的人物，例如：一个年轻的剑修，性格冷傲...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>1000字</Option>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.3}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <UserOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成人物设定'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'character.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><BookOutlined />剧情生成</span>} key=\"plot\">\n                <Form\n                  form={plotForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => generateContent('plot', values, batchMode)}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"剧情要求\"\n                    rules={[{ required: true, message: '请输入剧情要求' }]}\n                  >\n                    <TextArea\n                      rows={4}\n                      placeholder=\"请描述您想要生成的剧情，例如：主角在修仙门派中遇到的第一个挑战...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={8}>\n                      <Form.Item name=\"maxTokens\" label=\"最大字数\" initialValue={3000}>\n                        <Select>\n                          <Option value={2000}>2000字</Option>\n                          <Option value={3000}>3000字</Option>\n                          <Option value={5000}>5000字</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={8}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.8}>\n                        <Select>\n                          <Option value={0.5}>保守</Option>\n                          <Option value={0.8}>平衡</Option>\n                          <Option value={1.0}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={8}>\n                      <Form.Item name=\"plotType\" label=\"剧情类型\" initialValue=\"adventure\">\n                        <Select>\n                          <Option value=\"adventure\">冒险</Option>\n                          <Option value=\"romance\">爱情</Option>\n                          <Option value=\"conflict\">冲突</Option>\n                          <Option value=\"mystery\">悬疑</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={batchMode ? <ThunderboltOutlined /> : <BookOutlined />}\n                      >\n                        {batchMode ? `批量生成(${batchCount}个)` : '生成剧情大纲'}\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'plot.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"生成结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><EditOutlined />续写功能</span>} key=\"continue\">\n                <Form\n                  form={continueForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => {\n                    const prompt = values.continueHint\n                      ? `${values.prompt}\\n\\n续写提示：${values.continueHint}`\n                      : values.prompt;\n                    generateContent('continue-writing', { ...values, prompt });\n                  }}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"原文内容\"\n                    rules={[{ required: true, message: '请输入需要续写的原文内容' }]}\n                  >\n                    <TextArea\n                      rows={6}\n                      placeholder=\"请粘贴需要续写的原文内容...\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"continueHint\"\n                    label=\"续写提示（可选）\"\n                  >\n                    <TextArea\n                      rows={2}\n                      placeholder=\"可以提供续写的方向提示，例如：接下来主角遇到了...\"\n                    />\n                  </Form.Item>\n\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"maxTokens\" label=\"续写长度\" initialValue={2000}>\n                        <Select>\n                          <Option value={1000}>短篇(1000字)</Option>\n                          <Option value={2000}>中篇(2000字)</Option>\n                          <Option value={3000}>长篇(3000字)</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"temperature\" label=\"创意度\" initialValue={0.7}>\n                        <Select>\n                          <Option value={0.5}>保守</Option>\n                          <Option value={0.7}>平衡</Option>\n                          <Option value={0.9}>创新</Option>\n                        </Select>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={<EditOutlined />}\n                      >\n                        AI续写\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'continue.txt')}\n                          >\n                            下载\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"续写结果\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n\n              <TabPane tab={<span><CheckCircleOutlined />一致性检查</span>} key=\"check\">\n                <Form\n                  form={checkForm}\n                  layout=\"vertical\"\n                  onFinish={(values) => {\n                    const prompt = `检查类型：${values.checkType}\\n\\n内容：\\n${values.prompt}`;\n                    generateContent('check-consistency', { ...values, prompt });\n                  }}\n                >\n                  <Form.Item\n                    name=\"prompt\"\n                    label=\"检查内容\"\n                    rules={[{ required: true, message: '请输入需要检查的内容' }]}\n                  >\n                    <TextArea\n                      rows={8}\n                      placeholder=\"请粘贴需要进行一致性检查的小说内容...\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"checkType\"\n                    label=\"检查类型\"\n                    initialValue=\"all\"\n                  >\n                    <Select>\n                      <Option value=\"all\">全面检查</Option>\n                      <Option value=\"character\">人物一致性</Option>\n                      <Option value=\"plot\">情节逻辑</Option>\n                      <Option value=\"setting\">设定一致性</Option>\n                      <Option value=\"timeline\">时间线</Option>\n                    </Select>\n                  </Form.Item>\n\n                  <Form.Item>\n                    <Space>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        disabled={!aiStatus.connected}\n                        icon={<CheckCircleOutlined />}\n                      >\n                        开始检查\n                      </Button>\n                      {generatedContent && (\n                        <>\n                          <Button\n                            icon={<CopyOutlined />}\n                            onClick={() => copyToClipboard(generatedContent)}\n                          >\n                            复制\n                          </Button>\n                          <Button\n                            icon={<DownloadOutlined />}\n                            onClick={() => saveContent(generatedContent, 'check_report.txt')}\n                          >\n                            下载报告\n                          </Button>\n                        </>\n                      )}\n                    </Space>\n                  </Form.Item>\n\n                  {generatedContent && (\n                    <Form.Item label=\"检查报告\">\n                      <TextArea\n                        value={generatedContent}\n                        rows={8}\n                        readOnly\n                        style={{ backgroundColor: '#f5f5f5' }}\n                      />\n                    </Form.Item>\n                  )}\n                </Form>\n              </TabPane>\n            </Tabs>\n          </Card>\n        </Col>\n\n        <Col span={6}>\n          <Card title=\"AI设置\" size=\"small\">\n            <div style={{ marginBottom: '16px' }}>\n              <Text strong>AI提供商</Text>\n              <Select\n                style={{ width: '100%', marginTop: '8px' }}\n                value={currentProvider}\n                onChange={switchProvider}\n                loading={loading}\n              >\n                {providers.map(provider => (\n                  <Option key={provider} value={provider}>\n                    {provider.toUpperCase()}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            <Divider />\n\n            <div>\n              <Text strong>快速操作</Text>\n              <div style={{ marginTop: '8px' }}>\n                <Button\n                  block\n                  style={{ marginBottom: '8px' }}\n                  onClick={() => setMessages([])}\n                >\n                  清空对话\n                </Button>\n                <Button\n                  block\n                  onClick={fetchAIStatus}\n                  loading={loading}\n                >\n                  检查连接\n                </Button>\n              </div>\n            </div>\n          </Card>\n\n          {!aiStatus.connected && (\n            <Alert\n              style={{ marginTop: '16px' }}\n              message=\"AI服务离线\"\n              description=\"请检查AI服务配置或网络连接\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Col>\n      </Row>\n\n      {/* 新增的组件 */}\n      <AISettingsPanel />\n      <HistoryPanel />\n      <TemplatePanel />\n    </div>\n  );\n};\n\nexport default AIAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EAEJC,MAAM,EACNC,MAAM,EACNC,WAAW,EAEXC,KAAK,EACLC,UAAU,EAEVC,MAAM,QACD,MAAM;AACb,SACEC,aAAa,EACbC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EAEnBC,eAAe,EAEfC,gBAAgB,EAEhBC,YAAY,EACZC,cAAc,EACdC,eAAe,EAEfC,gBAAgB,EAChBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG7C,UAAU;AAC7C,MAAM;EAAE8C;AAAS,CAAC,GAAG5C,KAAK;AAC1B,MAAM;EAAE6C;AAAO,CAAC,GAAG5C,MAAM;AACzB,MAAM;EAAE6C;AAAQ,CAAC,GAAG1C,IAAI;AAGxB,MAAM2C,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC;IAAE+D,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAU,CAAC,CAAC;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqE,YAAY,CAAC,GAAG1D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACrC,MAAM,CAACC,QAAQ,CAAC,GAAG5D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACjC,MAAM,CAACE,YAAY,CAAC,GAAG7D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACrC,MAAM,CAACG,SAAS,CAAC,GAAG9D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EAClC,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM4E,cAAc,GAAG1E,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+E,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,GAAG;IACVC,iBAAiB,EAAE,GAAG;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMoG,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM3D,KAAK,CAAC4D,GAAG,CAAC,sBAAsB,CAAC;MACxD5C,YAAY,CAAC2C,QAAQ,CAACE,IAAI,CAAC9C,SAAS,CAAC;MACrCG,kBAAkB,CAACyC,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd7F,OAAO,CAAC6F,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAM3D,KAAK,CAAC4D,GAAG,CAAC,mBAAmB,CAAC;MACrDxC,WAAW,CAACuC,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,WAAW,CAAC;QAAEC,SAAS,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAM2C,cAAc,GAAG,MAAOC,QAAQ,IAAK;IACzC,IAAI;MACFpD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMd,KAAK,CAACmE,IAAI,CAAC,4BAA4B,EAAE;QAAED;MAAS,CAAC,CAAC;MAC5DhD,kBAAkB,CAACgD,QAAQ,CAAC;MAC5BhG,OAAO,CAACkG,OAAO,CAAC,QAAQF,QAAQ,EAAE,CAAC;MACnC,MAAMF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd7F,OAAO,CAAC6F,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC5C,YAAY,CAAC6C,IAAI,CAAC,CAAC,EAAE;IAE1B,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEhD;IAAa,CAAC;IAC3D,MAAMiD,WAAW,GAAG,CAAC,GAAGnD,QAAQ,EAAEgD,WAAW,CAAC;IAC9C/C,WAAW,CAACkD,WAAW,CAAC;IACxBhD,eAAe,CAAC,EAAE,CAAC;IACnBZ,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAM3D,KAAK,CAACmE,IAAI,CAAC,iBAAiB,EAAE;QACnD5C,QAAQ,EAAEmD;MACZ,CAAC,CAAC;MAEF,MAAMC,SAAS,GAAG;QAAEH,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACF;MAAS,CAAC;MACxEnC,WAAW,CAAC,CAAC,GAAGkD,WAAW,EAAEC,SAAS,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd7F,OAAO,CAAC6F,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8D,eAAe,GAAG,MAAAA,CAAOC,IAAI,EAAEC,MAAM,EAAEC,OAAO,GAAG,KAAK,KAAK;IAC/D,IAAI;MACFjE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkE,MAAM,GAAG;QACbC,MAAM,EAAEH,MAAM,CAACG,MAAM;QACrB/B,UAAU,EAAE4B,MAAM,CAACI,SAAS,IAAInC,QAAQ,CAACG,UAAU;QACnDD,WAAW,EAAE6B,MAAM,CAAC7B,WAAW,IAAIF,QAAQ,CAACE,WAAW;QACvD,GAAGF;MACL,CAAC;MAED,IAAIgC,OAAO,IAAIzB,SAAS,EAAE;QACxB;QACA,MAAM6B,OAAO,GAAG,EAAE;QAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,UAAU,EAAE4B,CAAC,EAAE,EAAE;UACnC,MAAMzB,QAAQ,GAAG,MAAM3D,KAAK,CAACmE,IAAI,CAAC,uBAAuBU,IAAI,EAAE,EAAEG,MAAM,CAAC;UACxEG,OAAO,CAACE,IAAI,CAAC;YACXC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGJ,CAAC;YAClBX,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;YAC9BI,IAAI,EAAEA,IAAI;YACVY,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,cAAc,CAAC,CAAC;YACtCxB,QAAQ,EAAEjD;UACZ,CAAC,CAAC;QACJ;;QAEA;QACAqB,iBAAiB,CAACqD,IAAI,IAAI,CAAC,GAAGR,OAAO,EAAE,GAAGQ,IAAI,CAAC,CAAC;QAChDvD,mBAAmB,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAACV,OAAO,CAAC;QACvCvG,OAAO,CAACkG,OAAO,CAAC,OAAOZ,UAAU,IAAIqB,IAAI,IAAI,CAAC;QAC9C,OAAOM,OAAO,CAAC,CAAC,CAAC,CAACV,OAAO;MAC3B,CAAC,MAAM;QACL;QACA,MAAMd,QAAQ,GAAG,MAAM3D,KAAK,CAACmE,IAAI,CAAC,uBAAuBU,IAAI,EAAE,EAAEG,MAAM,CAAC;QACxE,MAAMY,MAAM,GAAG;UACbN,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdf,OAAO,EAAEd,QAAQ,CAACE,IAAI,CAACY,OAAO;UAC9BI,IAAI,EAAEA,IAAI;UACVY,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,cAAc,CAAC,CAAC;UACtCxB,QAAQ,EAAEjD;QACZ,CAAC;;QAED;QACAqB,iBAAiB,CAACqD,IAAI,IAAI,CAACC,MAAM,EAAE,GAAGD,IAAI,CAAC,CAAC;QAC5CvD,mBAAmB,CAACuB,QAAQ,CAACE,IAAI,CAACY,OAAO,CAAC;QAC1CvG,OAAO,CAACkG,OAAO,CAAC,QAAQ,CAAC;QACzB,OAAOT,QAAQ,CAACE,IAAI,CAACY,OAAO;MAC9B;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd7F,OAAO,CAAC6F,KAAK,CAAC,KAAKc,IAAI,IAAI,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,WAAW,GAAGA,CAACpB,OAAO,EAAEqB,QAAQ,KAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACvB,OAAO,CAAC,EAAE;MAAEI,IAAI,EAAE;IAA2B,CAAC,CAAC;IACtE,MAAMoB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAGV,QAAQ,IAAI,cAAcP,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;IAC1Da,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;IACxB/H,OAAO,CAACkG,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM0C,eAAe,GAAIrC,OAAO,IAAK;IACnCsC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxC,OAAO,CAAC,CAACyC,IAAI,CAAC,MAAM;MAChDhJ,OAAO,CAACkG,OAAO,CAAC,WAAW,CAAC;IAC9B,CAAC,CAAC,CAAC+C,KAAK,CAAC,MAAM;MACbjJ,OAAO,CAAC6F,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqD,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,gBAAgB,GAAG,CACvB;MACE/B,EAAE,EAAE,CAAC;MACLgC,IAAI,EAAE,QAAQ;MACdzC,IAAI,EAAE,SAAS;MACfJ,OAAO,EAAE;IACX,CAAC,EACD;MACEa,EAAE,EAAE,CAAC;MACLgC,IAAI,EAAE,QAAQ;MACdzC,IAAI,EAAE,SAAS;MACfJ,OAAO,EAAE;IACX,CAAC,EACD;MACEa,EAAE,EAAE,CAAC;MACLgC,IAAI,EAAE,QAAQ;MACdzC,IAAI,EAAE,WAAW;MACjBJ,OAAO,EAAE;IACX,CAAC,EACD;MACEa,EAAE,EAAE,CAAC;MACLgC,IAAI,EAAE,QAAQ;MACdzC,IAAI,EAAE,WAAW;MACjBJ,OAAO,EAAE;IACX,CAAC,CACF;IACD3B,YAAY,CAACuE,gBAAgB,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,aAAa,GAAIC,QAAQ,IAAK;IAClC,IAAIA,QAAQ,CAAC3C,IAAI,KAAK,SAAS,EAAE;MAC/BlD,YAAY,CAAC8F,cAAc,CAAC;QAAExC,MAAM,EAAEuC,QAAQ,CAAC/C;MAAQ,CAAC,CAAC;MACzDxC,YAAY,CAAC,SAAS,CAAC;IACzB,CAAC,MAAM,IAAIuF,QAAQ,CAAC3C,IAAI,KAAK,WAAW,EAAE;MACxClD,YAAY,CAAC8F,cAAc,CAAC;QAAExC,MAAM,EAAEuC,QAAQ,CAAC/C;MAAQ,CAAC,CAAC;MACzDxC,YAAY,CAAC,WAAW,CAAC;IAC3B;IACAS,gBAAgB,CAAC,KAAK,CAAC;IACvBxE,OAAO,CAACkG,OAAO,CAAC,SAASoD,QAAQ,CAACF,IAAI,EAAE,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAzF,cAAc,CAAC4B,OAAO,cAAA6D,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDtK,SAAS,CAAC,MAAM;IACdmG,cAAc,CAAC,CAAC;IAChBM,aAAa,CAAC,CAAC;IACfoD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN7J,SAAS,CAAC,MAAM;IACdmK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACnG,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMuG,eAAe,GAAGA,CAAA,kBACtB5H,OAAA,CAAC9B,KAAK;IAAA2J,QAAA,gBACJ7H,OAAA,CAAC7B,GAAG;MAAC2J,KAAK,EAAE7G,QAAQ,CAACE,SAAS,GAAG,OAAO,GAAG,KAAM;MAAA0G,QAAA,EAC9C5G,QAAQ,CAACE,SAAS,GAAG,IAAI,GAAG;IAAI;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACNlI,OAAA,CAACI,IAAI;MAACuE,IAAI,EAAC,WAAW;MAAAkD,QAAA,GAAC,kCAAO,EAAC9G,eAAe;IAAA;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACtDlI,OAAA,CAACvC,MAAM;MACL0K,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEpI,OAAA,CAACb,cAAc;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBG,OAAO,EAAEvE,aAAc;MACvBnD,OAAO,EAAEA,OAAQ;MAAAkH,QAAA,EAClB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;MACL0K,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEpI,OAAA,CAACT,eAAe;QAAAwI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BG,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC,IAAI,CAAE;MAAAuF,QAAA,GACrC,2BACM,eAAA7H,OAAA,CAACrB,KAAK;QAAC2J,KAAK,EAAEnG,cAAc,CAACoG;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACTlI,OAAA,CAACvC,MAAM;MACL0K,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEpI,OAAA,CAACJ,gBAAgB;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3BG,OAAO,EAAEA,CAAA,KAAM7F,gBAAgB,CAAC,IAAI,CAAE;MAAAqF,QAAA,EACvC;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;MACL0K,IAAI,EAAC,OAAO;MACZC,IAAI,eAAEpI,OAAA,CAACL,eAAe;QAAAoI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BG,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,IAAI,CAAE;MAAAmF,QAAA,EACtC;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACR;;EAED;EACA,MAAMM,eAAe,GAAGA,CAAA,kBACtBxI,OAAA,CAACnB,MAAM;IACL4J,KAAK,EAAC,wCAAU;IAChBC,SAAS,EAAC,OAAO;IACjBC,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC,KAAK,CAAE;IACtCkG,IAAI,EAAEnG,YAAa;IACnBoG,KAAK,EAAE,GAAI;IAAAhB,QAAA,eAEX7H,OAAA,CAACjC,IAAI;MAAC+K,MAAM,EAAC,UAAU;MAAAjB,QAAA,gBACrB7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,wCAAoB;QAAAnB,QAAA,gBACnC7H,OAAA,CAACxB,MAAM;UACLyK,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEvG,QAAQ,CAACE,WAAY;UAC5BsG,QAAQ,EAAGD,KAAK,IAAKtG,WAAW,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE1C,WAAW,EAAEqG;UAAM,CAAC,CAAC,CAAE;UAC5EE,KAAK,EAAE;YACL,CAAC,EAAE,IAAI;YACP,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,IAAI;YACT,CAAC,EAAE;UACL;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,yBAAU;QAAAnB,QAAA,gBACzB7H,OAAA,CAACtB,WAAW;UACVuK,GAAG,EAAE,GAAI;UACTC,GAAG,EAAE,IAAK;UACVE,KAAK,EAAEvG,QAAQ,CAACG,UAAW;UAC3BqG,QAAQ,EAAGD,KAAK,IAAKtG,WAAW,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEzC,UAAU,EAAEoG;UAAM,CAAC,CAAC,CAAE;UAC3EG,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAO;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACFlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAnB,QAAA,gBACtB7H,OAAA,CAACxB,MAAM;UACLyK,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEvG,QAAQ,CAACI,KAAM;UACtBoG,QAAQ,EAAGD,KAAK,IAAKtG,WAAW,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAExC,KAAK,EAAEmG;UAAM,CAAC,CAAC,CAAE;UACtEE,KAAK,EAAE;YACL,CAAC,EAAE,GAAG;YACN,GAAG,EAAE,KAAK;YACV,CAAC,EAAE;UACL;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,8CAA0B;QAAAnB,QAAA,gBACzC7H,OAAA,CAACxB,MAAM;UACLyK,GAAG,EAAE,CAAC,CAAE;UACRC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEvG,QAAQ,CAACK,iBAAkB;UAClCmG,QAAQ,EAAGD,KAAK,IAAKtG,WAAW,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEvC,iBAAiB,EAAEkG;UAAM,CAAC,CAAC,CAAE;UAClFE,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,GAAG;YACR,GAAG,EAAE;UACP;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,6CAAyB;QAAAnB,QAAA,gBACxC7H,OAAA,CAACxB,MAAM;UACLyK,GAAG,EAAE,CAAC,CAAE;UACRC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,GAAI;UACVC,KAAK,EAAEvG,QAAQ,CAACM,gBAAiB;UACjCkG,QAAQ,EAAGD,KAAK,IAAKtG,WAAW,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEtC,gBAAgB,EAAEiG;UAAM,CAAC,CAAC,CAAE;UACjFE,KAAK,EAAE;YACL,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,GAAG;YACR,GAAG,EAAE;UACP;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEZlI,OAAA,CAAC5B,OAAO;QAAA2J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAACC,KAAK,EAAC,sCAAQ;QAAAnB,QAAA,eACvB7H,OAAA,CAAC9B,KAAK;UAACsL,SAAS,EAAC,UAAU;UAACD,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBACnD7H,OAAA,CAACvB,MAAM;YACLgL,OAAO,EAAErG,SAAU;YACnBiG,QAAQ,EAAEhG,YAAa;YACvBqG,eAAe,EAAC,cAAI;YACpBC,iBAAiB,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACD9E,SAAS,iBACRpD,OAAA,CAACtB,WAAW;YACVuK,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRE,KAAK,EAAE9F,UAAW;YAClB+F,QAAQ,EAAE9F,aAAc;YACxBqG,WAAW,EAAC,0BAAM;YAClBL,KAAK,EAAE;cAAEV,KAAK,EAAE;YAAO;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;QAAAlB,QAAA,eACR7H,OAAA,CAACvC,MAAM;UACLkH,IAAI,EAAC,SAAS;UACdkF,KAAK;UACLxB,OAAO,EAAEA,CAAA,KAAM;YACbrK,OAAO,CAACkG,OAAO,CAAC,SAAS,CAAC;YAC1BxB,eAAe,CAAC,KAAK,CAAC;UACxB,CAAE;UAAAmF,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACT;;EAED;EACA,MAAM4B,YAAY,GAAGA,CAAA,kBACnB9J,OAAA,CAAC1B,KAAK;IACJmK,KAAK,EAAC,sCAAQ;IACdG,IAAI,EAAEvG,WAAY;IAClB0H,QAAQ,EAAEA,CAAA,KAAMzH,cAAc,CAAC,KAAK,CAAE;IACtCuG,KAAK,EAAE,GAAI;IACXmB,MAAM,EAAE,cACNhK,OAAA,CAACvC,MAAM;MAAawM,MAAM;MAAC5B,OAAO,EAAEA,CAAA,KAAM;QACxCjG,iBAAiB,CAAC,EAAE,CAAC;QACrBpE,OAAO,CAACkG,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAE;MAAA2D,QAAA,EAAC;IAEH,GALY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CAAC,eACTlI,OAAA,CAACvC,MAAM;MAAa4K,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC,KAAK,CAAE;MAAAuF,QAAA,EAAC;IAE1D,GAFY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,CACT;IAAAL,QAAA,eAEF7H,OAAA,CAACzB,IAAI;MACH2L,UAAU,EAAE/H,cAAe;MAC3BgI,UAAU,EAAGC,IAAI,iBACfpK,OAAA,CAACzB,IAAI,CAACwK,IAAI;QACRsB,OAAO,EAAE,cACPrK,OAAA,CAACvC,MAAM;UACL0K,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEpI,OAAA,CAACP,YAAY;YAAAsI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACwD,IAAI,CAAC7F,OAAO,CAAE;UAAAsD,QAAA,EAC9C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;UACL0K,IAAI,EAAC,OAAO;UACZC,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACyE,IAAI,CAAC7F,OAAO,EAAE,GAAG6F,IAAI,CAACzF,IAAI,IAAIyF,IAAI,CAAChF,EAAE,MAAM,CAAE;UAAAyC,QAAA,EACzE;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA,CAACpB,UAAU;UACT6J,KAAK,EAAC,8DAAY;UAClB6B,SAAS,EAAEA,CAAA,KAAM;YACflI,iBAAiB,CAACqD,IAAI,IAAIA,IAAI,CAAC8E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpF,EAAE,KAAKgF,IAAI,CAAChF,EAAE,CAAC,CAAC;YAC7DpH,OAAO,CAACkG,OAAO,CAAC,OAAO,CAAC;UAC1B,CAAE;UAAA2D,QAAA,eAEF7H,OAAA,CAACvC,MAAM;YAAC0K,IAAI,EAAC,OAAO;YAAC8B,MAAM;YAAC7B,IAAI,eAAEpI,OAAA,CAACN,cAAc;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAAC;UAEtD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,CACb;QAAAL,QAAA,eAEF7H,OAAA,CAACzB,IAAI,CAACwK,IAAI,CAAC0B,IAAI;UACbhC,KAAK,eACHzI,OAAA,CAAC9B,KAAK;YAAA2J,QAAA,gBACJ7H,OAAA,CAAC7B,GAAG;cAAC2J,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEuC,IAAI,CAACzF;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnClI,OAAA,CAACI,IAAI;cAAAyH,QAAA,EAAEuC,IAAI,CAAC7E;YAAS;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlI,OAAA,CAAC7B,GAAG;cAAC2J,KAAK,EAAC,OAAO;cAAAD,QAAA,EAAEuC,IAAI,CAACpG;YAAQ;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACR;UACDwC,WAAW,eACT1K,OAAA,CAACK,SAAS;YACRsK,QAAQ,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAK,CAAE;YACxCtB,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAE,CAAE;YAAAjD,QAAA,EAE1BuC,IAAI,CAAC7F;UAAO;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACX;MACF6C,MAAM,EAAE;QAAEC,SAAS,EAAE;MAAS;IAAE;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;;EAED;EACA,MAAM+C,aAAa,GAAGA,CAAA,kBACpBjL,OAAA,CAAC1B,KAAK;IACJmK,KAAK,EAAC,oBAAK;IACXG,IAAI,EAAErG,aAAc;IACpBwH,QAAQ,EAAEA,CAAA,KAAMvH,gBAAgB,CAAC,KAAK,CAAE;IACxCqG,KAAK,EAAE,GAAI;IACXmB,MAAM,EAAE,cACNhK,OAAA,CAACvC,MAAM;MAAa4K,OAAO,EAAEA,CAAA,KAAM7F,gBAAgB,CAAC,KAAK,CAAE;MAAAqF,QAAA,EAAC;IAE5D,GAFY,OAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,CACT;IAAAL,QAAA,eAEF7H,OAAA,CAACzB,IAAI;MACH2L,UAAU,EAAEvH,SAAU;MACtBwH,UAAU,EAAG7C,QAAQ,iBACnBtH,OAAA,CAACzB,IAAI,CAACwK,IAAI;QACRsB,OAAO,EAAE,cACPrK,OAAA,CAACvC,MAAM;UACLkH,IAAI,EAAC,SAAS;UACdwD,IAAI,EAAC,OAAO;UACZE,OAAO,EAAEA,CAAA,KAAMhB,aAAa,CAACC,QAAQ,CAAE;UAAAO,QAAA,EACxC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,CACT;QAAAL,QAAA,eAEF7H,OAAA,CAACzB,IAAI,CAACwK,IAAI,CAAC0B,IAAI;UACbhC,KAAK,eACHzI,OAAA,CAAC9B,KAAK;YAAA2J,QAAA,gBACJ7H,OAAA,CAACI,IAAI;cAAC8K,MAAM;cAAArD,QAAA,EAAEP,QAAQ,CAACF;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnClI,OAAA,CAAC7B,GAAG;cAAC2J,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEP,QAAQ,CAAC3C;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACR;UACDwC,WAAW,eACT1K,OAAA,CAACK,SAAS;YACRsK,QAAQ,EAAE;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAK,CAAE;YACxCtB,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAE,CAAE;YAAAjD,QAAA,EAE1BP,QAAQ,CAAC/C;UAAO;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;;EAED;EACA,MAAMiD,aAAa,GAAGA,CAAA,kBACpBnL,OAAA;IAAKuJ,KAAK,EAAE;MAAE6B,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAzD,QAAA,gBACxE7H,OAAA;MAAKuJ,KAAK,EAAE;QAAEgC,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,MAAM,EAAE,mBAAmB;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAA9D,QAAA,GAC3GxG,QAAQ,CAACkH,MAAM,KAAK,CAAC,gBACpBvI,OAAA;QAAKuJ,KAAK,EAAE;UAAEqC,SAAS,EAAE,QAAQ;UAAE9D,KAAK,EAAE,MAAM;UAAE+D,SAAS,EAAE;QAAQ,CAAE;QAAAhE,QAAA,gBACrE7H,OAAA,CAAClB,aAAa;UAACyK,KAAK,EAAE;YAAEuC,QAAQ,EAAE,MAAM;YAAEhB,YAAY,EAAE;UAAO;QAAE;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpElI,OAAA;UAAA6H,QAAA,EAAK;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,GAEN7G,QAAQ,CAAC0K,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACtBjM,OAAA;QAAiBuJ,KAAK,EAAE;UAAEuB,YAAY,EAAE;QAAO,CAAE;QAAAjD,QAAA,eAC/C7H,OAAA;UAAKuJ,KAAK,EAAE;YAAE8B,OAAO,EAAE,MAAM;YAAEa,UAAU,EAAE;UAAa,CAAE;UAAArE,QAAA,GACvDmE,GAAG,CAAC1H,IAAI,KAAK,MAAM,gBAClBtE,OAAA,CAACd,YAAY;YAACqK,KAAK,EAAE;cAAE4C,WAAW,EAAE,KAAK;cAAEN,SAAS,EAAE;YAAM;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEjElI,OAAA,CAAClB,aAAa;YAACyK,KAAK,EAAE;cAAE4C,WAAW,EAAE,KAAK;cAAEN,SAAS,EAAE,KAAK;cAAE/D,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACpF,eACDlI,OAAA;YAAKuJ,KAAK,EAAE;cAAEgC,IAAI,EAAE;YAAE,CAAE;YAAA1D,QAAA,gBACtB7H,OAAA,CAACI,IAAI;cAAC8K,MAAM;cAAArD,QAAA,EAAEmE,GAAG,CAAC1H,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG;YAAM;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDlI,OAAA,CAACK,SAAS;cAACkJ,KAAK,EAAE;gBAAEsC,SAAS,EAAE,KAAK;gBAAEf,YAAY,EAAE;cAAE,CAAE;cAAAjD,QAAA,EACrDmE,GAAG,CAACzH;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAbE+D,KAAK;QAAAlE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcV,CACN,CACF,EACAvH,OAAO,iBACNX,OAAA;QAAKuJ,KAAK,EAAE;UAAEqC,SAAS,EAAE,QAAQ;UAAEH,OAAO,EAAE;QAAO,CAAE;QAAA5D,QAAA,gBACnD7H,OAAA,CAAC/B,IAAI;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC,eAAAlI,OAAA,CAACI,IAAI;UAACuE,IAAI,EAAC,WAAW;UAAAkD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACN,eACDlI,OAAA;QAAKoM,GAAG,EAAEpK;MAAe;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENlI,OAAA;MAAKuJ,KAAK,EAAE;QAAEsC,SAAS,EAAE;MAAO,CAAE;MAAAhE,QAAA,eAChC7H,OAAA,CAACtC,KAAK,CAAC2O,KAAK;QAACC,OAAO;QAAAzE,QAAA,gBAClB7H,OAAA,CAACtC,KAAK;UACJ6L,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAoB,CAAE;UACtC0D,WAAW,EAAC,yCAAW;UACvBnD,KAAK,EAAE7H,YAAa;UACpB8H,QAAQ,EAAGmD,CAAC,IAAKhL,eAAe,CAACgL,CAAC,CAACC,MAAM,CAACrD,KAAK,CAAE;UACjDsD,YAAY,EAAEvI,WAAY;UAC1BwI,QAAQ,EAAEhM,OAAO,IAAI,CAACM,QAAQ,CAACE;QAAU;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACFlI,OAAA,CAACvC,MAAM;UACLkH,IAAI,EAAC,SAAS;UACdyD,IAAI,eAAEpI,OAAA,CAAChB,YAAY;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAElE,WAAY;UACrBxD,OAAO,EAAEA,OAAQ;UACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;UAAA0G,QAAA,EAC/B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACElI,OAAA;IAAK4M,SAAS,EAAC,SAAS;IAAA/E,QAAA,gBACtB7H,OAAA;MAAK4M,SAAS,EAAC,aAAa;MAAA/E,QAAA,gBAC1B7H,OAAA,CAACG,KAAK;QAAC0M,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAA/E,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpDlI,OAAA,CAAC4H,eAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENlI,OAAA,CAACpC,GAAG;MAACkP,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAjF,QAAA,gBACpB7H,OAAA,CAACnC,GAAG;QAACkP,IAAI,EAAE,EAAG;QAAAlF,QAAA,eACZ7H,OAAA,CAACzC,IAAI;UAAAsK,QAAA,eACH7H,OAAA,CAAClC,IAAI;YAACkP,SAAS,EAAElL,SAAU;YAACuH,QAAQ,EAAEtH,YAAa;YAAA8F,QAAA,gBACjD7H,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACjB,eAAe;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eACjD7H,OAAA,CAACmL,aAAa;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GADqC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAErD,CAAC,eAEVlI,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACf,YAAY;kBAAA8I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C7H,OAAA,CAACjC,IAAI;gBACHmP,IAAI,EAAEzL,YAAa;gBACnBqH,MAAM,EAAC,UAAU;gBACjBqE,QAAQ,EAAGvI,MAAM,IAAKF,eAAe,CAAC,SAAS,EAAEE,MAAM,EAAExB,SAAS,CAAE;gBAAAyE,QAAA,gBAEpE7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZoE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAErP,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA6J,QAAA,eAEhD7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAAkC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACpC,GAAG;kBAACkP,MAAM,EAAE,EAAG;kBAAAjF,QAAA,gBACd7H,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAACsE,YAAY,EAAE,IAAK;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlI,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAACsE,YAAY,EAAE,GAAI;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAAAlB,QAAA,eACR7H,OAAA,CAAC9B,KAAK;oBAAA2J,QAAA,gBACJ7H,OAAA,CAACvC,MAAM;sBACLkH,IAAI,EAAC,SAAS;sBACd4I,QAAQ,EAAC,QAAQ;sBACjB5M,OAAO,EAAEA,OAAQ;sBACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;sBAC9BiH,IAAI,EAAEhF,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACf,YAAY;wBAAA8I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DzE,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRjG,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA2H,QAAA,gBACE7H,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACP,YAAY;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC3E,gBAAgB,CAAE;wBAAA4F,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC1D,gBAAgB,EAAE,aAAa,CAAE;wBAAA4F,QAAA,EAC7D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXjG,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB7H,OAAA,CAACM,QAAQ;oBACP8I,KAAK,EAAEnH,gBAAiB;oBACxB2I,IAAI,EAAE,CAAE;oBACR4C,QAAQ;oBACRjE,KAAK,EAAE;sBAAEkE,eAAe,EAAE;oBAAU;kBAAE;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA9E4C,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+ErD,CAAC,eAEVlI,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACd,YAAY;kBAAA6I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C7H,OAAA,CAACjC,IAAI;gBACH+K,MAAM,EAAC,UAAU;gBACjBqE,QAAQ,EAAGvI,MAAM,IAAKF,eAAe,CAAC,WAAW,EAAEE,MAAM,EAAExB,SAAS,CAAE;gBAAAyE,QAAA,gBAEtE7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZoE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAErP,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA6J,QAAA,eAEhD7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAAgC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACpC,GAAG;kBAACkP,MAAM,EAAE,EAAG;kBAAAjF,QAAA,gBACd7H,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAACsE,YAAY,EAAE,IAAK;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlI,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAACsE,YAAY,EAAE,GAAI;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAAAlB,QAAA,eACR7H,OAAA,CAAC9B,KAAK;oBAAA2J,QAAA,gBACJ7H,OAAA,CAACvC,MAAM;sBACLkH,IAAI,EAAC,SAAS;sBACd4I,QAAQ,EAAC,QAAQ;sBACjB5M,OAAO,EAAEA,OAAQ;sBACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;sBAC9BiH,IAAI,EAAEhF,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACd,YAAY;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DzE,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRjG,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA2H,QAAA,gBACE7H,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACP,YAAY;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC3E,gBAAgB,CAAE;wBAAA4F,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC1D,gBAAgB,EAAE,eAAe,CAAE;wBAAA4F,QAAA,EAC/D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXjG,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB7H,OAAA,CAACM,QAAQ;oBACP8I,KAAK,EAAEnH,gBAAiB;oBACxB2I,IAAI,EAAE,CAAE;oBACR4C,QAAQ;oBACRjE,KAAK,EAAE;sBAAEkE,eAAe,EAAE;oBAAU;kBAAE;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA7E4C,WAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8EvD,CAAC,eAEVlI,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACZ,YAAY;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C7H,OAAA,CAACjC,IAAI;gBACHmP,IAAI,EAAEvL,QAAS;gBACfmH,MAAM,EAAC,UAAU;gBACjBqE,QAAQ,EAAGvI,MAAM,IAAKF,eAAe,CAAC,MAAM,EAAEE,MAAM,EAAExB,SAAS,CAAE;gBAAAyE,QAAA,gBAEjE7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZoE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAErP,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA6J,QAAA,eAEhD7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAAoC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACpC,GAAG;kBAACkP,MAAM,EAAE,EAAG;kBAAAjF,QAAA,gBACd7H,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,CAAE;oBAAAlF,QAAA,eACX7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAACsE,YAAY,EAAE,IAAK;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAK;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlI,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,CAAE;oBAAAlF,QAAA,eACX7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAACsE,YAAY,EAAE,GAAI;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlI,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,CAAE;oBAAAlF,QAAA,eACX7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,UAAU;sBAAC4B,KAAK,EAAC,0BAAM;sBAACsE,YAAY,EAAC,WAAW;sBAAAzF,QAAA,eAC9D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAC,WAAW;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACrClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAC,SAAS;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAC,UAAU;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAC,SAAS;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAAAlB,QAAA,eACR7H,OAAA,CAAC9B,KAAK;oBAAA2J,QAAA,gBACJ7H,OAAA,CAACvC,MAAM;sBACLkH,IAAI,EAAC,SAAS;sBACd4I,QAAQ,EAAC,QAAQ;sBACjB5M,OAAO,EAAEA,OAAQ;sBACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;sBAC9BiH,IAAI,EAAEhF,SAAS,gBAAGpD,OAAA,CAACH,mBAAmB;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACZ,YAAY;wBAAA2I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAE5DzE,SAAS,GAAG,QAAQE,UAAU,IAAI,GAAG;oBAAQ;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,EACRjG,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA2H,QAAA,gBACE7H,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACP,YAAY;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC3E,gBAAgB,CAAE;wBAAA4F,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC1D,gBAAgB,EAAE,UAAU,CAAE;wBAAA4F,QAAA,EAC1D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXjG,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB7H,OAAA,CAACM,QAAQ;oBACP8I,KAAK,EAAEnH,gBAAiB;oBACxB2I,IAAI,EAAE,CAAE;oBACR4C,QAAQ;oBACRjE,KAAK,EAAE;sBAAEkE,eAAe,EAAE;oBAAU;kBAAE;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GAxF4C,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyFlD,CAAC,eAEVlI,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACX,YAAY;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eAC9C7H,OAAA,CAACjC,IAAI;gBACHmP,IAAI,EAAEtL,YAAa;gBACnBkH,MAAM,EAAC,UAAU;gBACjBqE,QAAQ,EAAGvI,MAAM,IAAK;kBACpB,MAAMG,MAAM,GAAGH,MAAM,CAAC8I,YAAY,GAC9B,GAAG9I,MAAM,CAACG,MAAM,YAAYH,MAAM,CAAC8I,YAAY,EAAE,GACjD9I,MAAM,CAACG,MAAM;kBACjBL,eAAe,CAAC,kBAAkB,EAAE;oBAAE,GAAGE,MAAM;oBAAEG;kBAAO,CAAC,CAAC;gBAC5D,CAAE;gBAAA8C,QAAA,gBAEF7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZoE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAErP,OAAO,EAAE;kBAAe,CAAC,CAAE;kBAAA6J,QAAA,eAErD7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAAiB;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,cAAc;kBACnB4B,KAAK,EAAC,kDAAU;kBAAAnB,QAAA,eAEhB7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAA4B;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACpC,GAAG;kBAACkP,MAAM,EAAE,EAAG;kBAAAjF,QAAA,gBACd7H,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,WAAW;sBAAC4B,KAAK,EAAC,0BAAM;sBAACsE,YAAY,EAAE,IAAK;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,IAAK;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlI,OAAA,CAACnC,GAAG;oBAACkP,IAAI,EAAE,EAAG;oBAAAlF,QAAA,eACZ7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;sBAAC3B,IAAI,EAAC,aAAa;sBAAC4B,KAAK,EAAC,oBAAK;sBAACsE,YAAY,EAAE,GAAI;sBAAAzF,QAAA,eAC1D7H,OAAA,CAACrC,MAAM;wBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC/BlI,OAAA,CAACO,MAAM;0BAAC6I,KAAK,EAAE,GAAI;0BAAAvB,QAAA,EAAC;wBAAE;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAAAlB,QAAA,eACR7H,OAAA,CAAC9B,KAAK;oBAAA2J,QAAA,gBACJ7H,OAAA,CAACvC,MAAM;sBACLkH,IAAI,EAAC,SAAS;sBACd4I,QAAQ,EAAC,QAAQ;sBACjB5M,OAAO,EAAEA,OAAQ;sBACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;sBAC9BiH,IAAI,eAAEpI,OAAA,CAACX,YAAY;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EACxB;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRjG,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA2H,QAAA,gBACE7H,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACP,YAAY;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC3E,gBAAgB,CAAE;wBAAA4F,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC1D,gBAAgB,EAAE,cAAc,CAAE;wBAAA4F,QAAA,EAC9D;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXjG,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB7H,OAAA,CAACM,QAAQ;oBACP8I,KAAK,EAAEnH,gBAAiB;oBACxB2I,IAAI,EAAE,CAAE;oBACR4C,QAAQ;oBACRjE,KAAK,EAAE;sBAAEkE,eAAe,EAAE;oBAAU;kBAAE;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA7F4C,UAAU;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FtD,CAAC,eAEVlI,OAAA,CAACQ,OAAO;cAACyM,GAAG,eAAEjN,OAAA;gBAAA6H,QAAA,gBAAM7H,OAAA,CAACV,mBAAmB;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kCAAK;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAE;cAAAL,QAAA,eACtD7H,OAAA,CAACjC,IAAI;gBACHmP,IAAI,EAAErL,SAAU;gBAChBiH,MAAM,EAAC,UAAU;gBACjBqE,QAAQ,EAAGvI,MAAM,IAAK;kBACpB,MAAMG,MAAM,GAAG,QAAQH,MAAM,CAAC+I,SAAS,YAAY/I,MAAM,CAACG,MAAM,EAAE;kBAClEL,eAAe,CAAC,mBAAmB,EAAE;oBAAE,GAAGE,MAAM;oBAAEG;kBAAO,CAAC,CAAC;gBAC7D,CAAE;gBAAA8C,QAAA,gBAEF7H,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAC,0BAAM;kBACZoE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAErP,OAAO,EAAE;kBAAa,CAAC,CAAE;kBAAA6J,QAAA,eAEnD7H,OAAA,CAACM,QAAQ;oBACPsK,IAAI,EAAE,CAAE;oBACR2B,WAAW,EAAC;kBAAsB;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBACR3B,IAAI,EAAC,WAAW;kBAChB4B,KAAK,EAAC,0BAAM;kBACZsE,YAAY,EAAC,KAAK;kBAAAzF,QAAA,eAElB7H,OAAA,CAACrC,MAAM;oBAAAkK,QAAA,gBACL7H,OAAA,CAACO,MAAM;sBAAC6I,KAAK,EAAC,KAAK;sBAAAvB,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjClI,OAAA,CAACO,MAAM;sBAAC6I,KAAK,EAAC,WAAW;sBAAAvB,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxClI,OAAA,CAACO,MAAM;sBAAC6I,KAAK,EAAC,MAAM;sBAAAvB,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClClI,OAAA,CAACO,MAAM;sBAAC6I,KAAK,EAAC,SAAS;sBAAAvB,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtClI,OAAA,CAACO,MAAM;sBAAC6I,KAAK,EAAC,UAAU;sBAAAvB,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEZlI,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAAAlB,QAAA,eACR7H,OAAA,CAAC9B,KAAK;oBAAA2J,QAAA,gBACJ7H,OAAA,CAACvC,MAAM;sBACLkH,IAAI,EAAC,SAAS;sBACd4I,QAAQ,EAAC,QAAQ;sBACjB5M,OAAO,EAAEA,OAAQ;sBACjBgM,QAAQ,EAAE,CAAC1L,QAAQ,CAACE,SAAU;sBAC9BiH,IAAI,eAAEpI,OAAA,CAACV,mBAAmB;wBAAAyI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAL,QAAA,EAC/B;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRjG,gBAAgB,iBACfjC,OAAA,CAAAE,SAAA;sBAAA2H,QAAA,gBACE7H,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACP,YAAY;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACvBG,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAAC3E,gBAAgB,CAAE;wBAAA4F,QAAA,EAClD;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;wBACL2K,IAAI,eAAEpI,OAAA,CAACR,gBAAgB;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAC3BG,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAAC1D,gBAAgB,EAAE,kBAAkB,CAAE;wBAAA4F,QAAA,EAClE;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAEXjG,gBAAgB,iBACfjC,OAAA,CAACjC,IAAI,CAACgL,IAAI;kBAACC,KAAK,EAAC,0BAAM;kBAAAnB,QAAA,eACrB7H,OAAA,CAACM,QAAQ;oBACP8I,KAAK,EAAEnH,gBAAiB;oBACxB2I,IAAI,EAAE,CAAE;oBACR4C,QAAQ;oBACRjE,KAAK,EAAE;sBAAEkE,eAAe,EAAE;oBAAU;kBAAE;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA1EoD,OAAO;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2E3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENlI,OAAA,CAACnC,GAAG;QAACkP,IAAI,EAAE,CAAE;QAAAlF,QAAA,gBACX7H,OAAA,CAACzC,IAAI;UAACkL,KAAK,EAAC,gBAAM;UAACN,IAAI,EAAC,OAAO;UAAAN,QAAA,gBAC7B7H,OAAA;YAAKuJ,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAAjD,QAAA,gBACnC7H,OAAA,CAACI,IAAI;cAAC8K,MAAM;cAAArD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBlI,OAAA,CAACrC,MAAM;cACL4L,KAAK,EAAE;gBAAEV,KAAK,EAAE,MAAM;gBAAEgD,SAAS,EAAE;cAAM,CAAE;cAC3CzC,KAAK,EAAErI,eAAgB;cACvBsI,QAAQ,EAAEtF,cAAe;cACzBpD,OAAO,EAAEA,OAAQ;cAAAkH,QAAA,EAEhBhH,SAAS,CAACkL,GAAG,CAAC/H,QAAQ,iBACrBhE,OAAA,CAACO,MAAM;gBAAgB6I,KAAK,EAAEpF,QAAS;gBAAA6D,QAAA,EACpC7D,QAAQ,CAAC4J,WAAW,CAAC;cAAC,GADZ5J,QAAQ;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlI,OAAA,CAAC5B,OAAO;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEXlI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA,CAACI,IAAI;cAAC8K,MAAM;cAAArD,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxBlI,OAAA;cAAKuJ,KAAK,EAAE;gBAAEsC,SAAS,EAAE;cAAM,CAAE;cAAAhE,QAAA,gBAC/B7H,OAAA,CAACvC,MAAM;gBACLoM,KAAK;gBACLN,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAM,CAAE;gBAC/BzC,OAAO,EAAEA,CAAA,KAAM/G,WAAW,CAAC,EAAE,CAAE;gBAAAuG,QAAA,EAChC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlI,OAAA,CAACvC,MAAM;gBACLoM,KAAK;gBACLxB,OAAO,EAAEvE,aAAc;gBACvBnD,OAAO,EAAEA,OAAQ;gBAAAkH,QAAA,EAClB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAEN,CAACjH,QAAQ,CAACE,SAAS,iBAClBnB,OAAA,CAAC3B,KAAK;UACJkL,KAAK,EAAE;YAAEsC,SAAS,EAAE;UAAO,CAAE;UAC7B7N,OAAO,EAAC,4BAAQ;UAChB0M,WAAW,EAAC,4EAAgB;UAC5B/F,IAAI,EAAC,SAAS;UACdkJ,QAAQ;QAAA;UAAA9F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlI,OAAA,CAACwI,eAAe;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBlI,OAAA,CAAC8J,YAAY;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBlI,OAAA,CAACiL,aAAa;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACxH,EAAA,CAtjCID,WAAW;EAAA,QAOQ1C,IAAI,CAAC2D,OAAO,EAChB3D,IAAI,CAAC2D,OAAO,EACR3D,IAAI,CAAC2D,OAAO,EACf3D,IAAI,CAAC2D,OAAO;AAAA;AAAAoM,EAAA,GAV5BrN,WAAW;AAwjCjB,eAAeA,WAAW;AAAC,IAAAqN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}