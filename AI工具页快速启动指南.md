# AI工具页快速启动指南

## 🚀 快速开始

### 1. 系统启动
```bash
# 启动系统（使用中文支持的终端）
./Start-System.bat

# 或者手动启动
cd backend && python run.py
cd frontend && npm start
```

### 2. AI配置
1. 打开浏览器访问 `http://localhost:3000`
2. 进入 **系统设置** → **AI配置**
3. 选择AI提供商并配置API密钥
4. 点击 **测试连接** 验证配置
5. 保存设置

### 3. 访问AI工具页
- 点击左侧导航栏的 **AI助手**
- 或直接访问 `http://localhost:3000/ai-assistant`

## 🎯 功能快速体验

### 第一次使用建议流程

#### 步骤1: 智能对话热身
1. 切换到 **智能对话** 标签
2. 输入: "你好，请介绍一下你的功能"
3. 体验AI的响应和对话界面

#### 步骤2: 使用模板快速生成
1. 点击顶部的 **模板库** 按钮
2. 选择 "玄幻世界设定" 模板
3. 点击 **应用模板**
4. 自动跳转到设定生成页面并填充内容
5. 点击 **生成世界设定** 体验生成功能

#### 步骤3: 尝试批量生成
1. 点击顶部的 **高级设置** 按钮
2. 开启 **批量生成模式**，设置生成数量为3
3. 保存设置
4. 返回任意生成页面，注意按钮变为 "批量生成(3个)"
5. 体验一次生成多个结果的功能

#### 步骤4: 查看历史记录
1. 点击顶部的 **历史记录** 按钮
2. 查看刚才生成的内容
3. 尝试复制、下载功能

#### 步骤5: 体验新功能
1. **剧情生成**: 创建故事大纲
2. **续写功能**: 基于现有文本续写
3. **一致性检查**: 检查内容逻辑

## ⚙️ 参数调节指南

### 温度参数 (Temperature)
- **0.1-0.3**: 保守模式，适合正式文档
- **0.4-0.7**: 平衡模式，适合大多数创作
- **0.8-1.0**: 创新模式，适合创意写作
- **1.1-2.0**: 随机模式，适合头脑风暴

### 最大Token数
- **500-1000**: 短文本，适合简介、摘要
- **1000-2000**: 中等长度，适合章节、设定
- **2000-5000**: 长文本，适合详细描述
- **5000+**: 超长文本，适合完整故事

### 使用建议
1. **首次使用**: 保持默认参数
2. **内容太保守**: 提高温度参数
3. **内容太随意**: 降低温度参数
4. **需要更多内容**: 增加Token数
5. **内容太冗长**: 减少Token数

## 🛠️ 常见问题解决

### Q1: AI服务显示离线
**解决方案:**
1. 检查网络连接
2. 验证API密钥是否正确
3. 确认服务地址配置
4. 尝试切换其他AI提供商

### Q2: 生成内容质量不佳
**解决方案:**
1. 优化提示词描述
2. 调整温度参数
3. 尝试批量生成选择最佳结果
4. 使用模板作为起点

### Q3: 生成速度慢
**解决方案:**
1. 减少Token数设置
2. 切换到响应更快的AI提供商
3. 检查网络状况
4. 避免在高峰期使用

### Q4: 无法保存历史记录
**解决方案:**
1. 检查浏览器存储权限
2. 清除浏览器缓存重试
3. 确保有足够的存储空间

## 📚 高级使用技巧

### 1. 组合使用多功能
```
创作流程示例:
设定生成 → 人物生成 → 剧情生成 → 续写功能 → 一致性检查
```

### 2. 模板自定义
- 基于预设模板修改
- 保存常用的提示词
- 建立个人创作模板库

### 3. 参数预设
- 为不同类型内容设置不同参数
- 保存常用参数组合
- 根据AI提供商调整参数

### 4. 批量生成策略
- 用于获得多个创意选择
- 对比不同参数效果
- 寻找最佳表达方式

### 5. 历史记录管理
- 定期整理和分类
- 导出重要内容
- 建立个人素材库

## 🎯 最佳实践

### 提示词编写技巧
1. **具体明确**: 详细描述需求
2. **结构清晰**: 使用分点或分段
3. **示例引导**: 提供期望的格式示例
4. **约束条件**: 明确字数、风格等要求

### 参数调节策略
1. **渐进调整**: 小幅度调整参数观察效果
2. **记录最佳**: 记录效果好的参数组合
3. **场景适配**: 不同内容类型使用不同参数
4. **提供商特性**: 了解不同AI的特点

### 内容管理建议
1. **及时保存**: 生成好的内容立即保存
2. **分类整理**: 按类型和项目分类
3. **版本控制**: 保留不同版本的内容
4. **备份重要**: 定期备份重要创作

## 🔧 故障排除

### 系统启动问题
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# 重启服务
./Stop-System.bat
./Start-System.bat
```

### 依赖问题
```bash
# 重新安装依赖
cd backend && pip install -r requirements.txt
cd frontend && npm install
```

### 配置问题
1. 检查 `.env` 文件配置
2. 验证API密钥格式
3. 确认服务地址正确

## 📞 技术支持

如遇到问题，请按以下顺序尝试:
1. 查看本指南的故障排除部分
2. 检查系统日志文件
3. 参考官方文档
4. 联系技术支持

---

**祝您使用愉快！开始您的AI辅助创作之旅吧！** 🚀✨
