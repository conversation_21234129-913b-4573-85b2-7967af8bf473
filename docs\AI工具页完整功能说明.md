# AI工具页完整功能说明

## 概述

AI工具页是NovelCraft小说管理系统的核心功能模块，提供了全面的AI辅助创作工具。经过完善后，现在支持多种AI生成功能、高级参数调节、批量生成、历史记录管理等强大功能。

## 主要功能

### 1. 智能对话 💬
- **功能描述**: 与AI进行自然语言交互，获得创作建议和灵感
- **特色功能**:
  - 多轮对话支持
  - 实时消息流显示
  - 自动滚动到最新消息
  - 对话历史保存
  - 一键清空对话

### 2. 设定生成 🌍
- **功能描述**: 生成详细的世界观和背景设定
- **生成内容**:
  - 世界观背景
  - 地理环境描述
  - 历史文化设定
  - 政治体系结构
  - 经济制度说明
  - 特殊规则或法则
- **支持功能**:
  - 批量生成多个版本
  - 参数调节（字数、创意度）
  - 结果预览和编辑
  - 一键复制和下载

### 3. 人物生成 👤
- **功能描述**: 创建丰富立体的角色设定
- **生成内容**:
  - 基本信息（姓名、年龄、性别、身份）
  - 外貌特征描述
  - 性格特点分析
  - 能力技能设定
  - 背景故事编写
  - 人际关系网络
  - 成长轨迹规划
- **支持功能**:
  - 批量生成多个角色
  - 角色类型选择
  - 详细程度控制

### 4. 剧情生成 📖
- **功能描述**: 生成引人入胜的故事情节
- **生成内容**:
  - 主要情节线设计
  - 关键转折点安排
  - 人物冲突设置
  - 情节发展脉络
  - 高潮部分设计
  - 结局安排建议
- **特色功能**:
  - 剧情类型选择（冒险、爱情、冲突、悬疑）
  - 长度控制（2000-5000字）
  - 创意度调节

### 5. 续写功能 ✍️
- **功能描述**: 基于现有内容进行智能续写
- **核心特点**:
  - 保持文风一致性
  - 情节自然流畅
  - 人物性格符合设定
  - 推进故事发展
  - 保持悬念和张力
- **使用方式**:
  - 粘贴原文内容
  - 可选续写提示
  - 长度选择（短篇/中篇/长篇）

### 6. 一致性检查 ✅
- **功能描述**: 检查内容的逻辑一致性
- **检查项目**:
  - 人物设定前后一致性
  - 世界观设定矛盾检查
  - 时间线合理性验证
  - 情节逻辑通顺性
  - 细节描述冲突检测
- **检查类型**:
  - 全面检查
  - 人物一致性专项
  - 情节逻辑专项
  - 设定一致性专项
  - 时间线专项

## 高级功能

### 1. AI参数设置 ⚙️
- **温度参数 (Temperature)**: 控制输出的随机性和创造性
  - 0-0.3: 保守，输出更稳定
  - 0.4-0.8: 平衡，适合大多数场景
  - 0.9-1.4: 创新，更有创意
  - 1.5-2.0: 随机，高度创新但可能不稳定

- **最大Token数**: 控制生成内容的长度
  - 100-1000: 短文本
  - 1000-3000: 中等长度
  - 3000-8000: 长文本

- **Top P**: 控制词汇选择的多样性
  - 0.1-0.5: 保守选择
  - 0.6-0.9: 平衡选择
  - 0.9-1.0: 多样化选择

- **频率惩罚 (Frequency Penalty)**: 减少重复内容
  - -2到0: 允许重复
  - 0到1: 适度减少重复
  - 1到2: 强力避免重复

- **存在惩罚 (Presence Penalty)**: 鼓励讨论新话题
  - -2到0: 专注当前话题
  - 0到1: 适度扩展话题
  - 1到2: 强力引入新话题

### 2. 批量生成 🔄
- **功能描述**: 一次生成多个结果供选择
- **支持范围**: 2-10个结果
- **适用场景**:
  - 需要多个创意选择
  - 对比不同风格
  - 寻找最佳方案

### 3. 历史记录管理 📚
- **记录内容**:
  - 生成时间戳
  - 内容类型标识
  - AI提供商信息
  - 完整生成内容
- **管理功能**:
  - 查看历史记录
  - 复制到剪贴板
  - 下载为文件
  - 删除单条记录
  - 清空全部记录

### 4. 模板库 📋
- **预设模板**:
  - 玄幻世界设定模板
  - 现代都市背景模板
  - 主角人物模板
  - 反派角色模板
- **使用方式**:
  - 一键应用模板
  - 自动填充表单
  - 快速开始创作

## 技术特性

### 1. 多AI提供商支持
- **OpenAI**: GPT-3.5/4系列
- **Claude**: Anthropic的Claude系列
- **智谱AI**: GLM-4等国产模型
- **硅基流动**: DeepSeek、Qwen等
- **Google AI**: Gemini系列
- **Grok**: xAI的Grok模型
- **Ollama**: 本地部署开源模型
- **自定义**: 兼容OpenAI API的服务

### 2. 实时状态监控
- AI服务连接状态
- 当前使用的提供商
- 服务健康度检查
- 错误信息提示

### 3. 用户体验优化
- 响应式界面设计
- 加载状态指示
- 错误处理和提示
- 快捷操作按钮
- 键盘快捷键支持

## 使用指南

### 1. 基础使用流程
1. 确保AI服务已配置并连接
2. 选择需要的功能标签页
3. 填写生成要求或输入内容
4. 调整参数设置（可选）
5. 点击生成按钮
6. 查看结果并进行后续操作

### 2. 高级使用技巧
1. **参数调节**: 根据需求调整AI参数获得更好效果
2. **模板应用**: 使用预设模板快速开始
3. **批量生成**: 开启批量模式获得多个选择
4. **历史管理**: 利用历史记录管理创作内容
5. **组合使用**: 结合多个功能完成复杂创作任务

### 3. 最佳实践
1. **明确需求**: 详细描述生成要求
2. **合理参数**: 根据内容类型选择合适参数
3. **迭代优化**: 基于结果调整要求和参数
4. **内容管理**: 及时保存有价值的生成内容
5. **一致性检查**: 定期检查内容的逻辑一致性

## 故障排除

### 常见问题
1. **AI服务离线**: 检查网络连接和API配置
2. **生成失败**: 确认API密钥和服务地址正确
3. **响应缓慢**: 调整参数或切换AI提供商
4. **内容质量不佳**: 优化提示词和参数设置

### 技术支持
- 查看系统日志获取详细错误信息
- 使用连接测试功能诊断问题
- 参考AI提供商的官方文档
- 联系技术支持获取帮助

---

*本文档持续更新，如有问题请及时反馈。*
