# NovelCraft - 小说管理系统

## 快速启动

### 启动方式

**推荐使用以下启动脚本：**

1. **Start-System.bat** - 一键启动脚本（推荐）
2. **Stop-System.bat** - 一键停止脚本
3. **Install-Dependencies.bat** - 依赖安装脚本

### 启动步骤

1. 双击运行 `Start-System.bat`
2. 等待后端和前端服务启动
3. 浏览器会自动打开前端界面 (http://localhost:3000)
4. API文档地址: http://localhost:8000/docs

### 停止服务

- 双击运行 `Stop-System.bat` 停止所有服务
- 或者在启动窗口中按 `Ctrl+C` 停止服务

### 系统要求

#### 环境依赖
- **Python**: 3.9或更高版本
- **Node.js**: 16或更高版本
- **操作系统**: Windows 10/11（推荐）

#### 快速安装
1. 双击运行 `Install-Dependencies.bat` - 自动安装所有依赖
2. 双击运行 `Start-System.bat` - 启动NovelCraft系统
3. 双击运行 `Stop-System.bat` - 停止所有服务

### 启动流程详解

#### 第一次使用
1. **安装依赖**: 双击 `Install-Dependencies.bat`
   - 自动检查Python和Node.js环境
   - 安装后端Python依赖包
   - 安装前端Node.js依赖包

2. **启动系统**: 双击 `Start-System.bat`
   - 自动检查环境和依赖
   - 启动后端API服务（端口8000）
   - 启动前端界面服务（端口3000）
   - 自动打开浏览器访问系统

3. **停止系统**: 双击 `Stop-System.bat`
   - 安全停止所有相关进程
   - 清理临时文件和窗口

#### 日常使用
- **启动**: 双击 `Start-System.bat`
- **停止**: 双击 `Stop-System.bat`

### 故障排除

#### 常见问题
1. **Python环境问题**: 确保已安装Python 3.9+并添加到PATH
2. **Node.js环境问题**: 确保已安装Node.js 16+并添加到PATH
3. **端口占用**: 确保8000和3000端口未被其他程序占用
4. **网络问题**: 依赖安装需要网络连接，请检查网络设置

#### 解决方案
- 重新运行 `Install-Dependencies.bat` 修复依赖问题
- 运行 `Stop-System.bat` 清理进程后重新启动
- 检查防火墙和杀毒软件设置

### Ollama问题排查

#### 快速检查
- 双击运行 `Check-Ollama.bat` - 自动检查Ollama状态
- 运行 `python ollama_diagnostic.py` - 详细诊断
- 运行 `python fix_ollama_issues.py` - 自动修复

#### 常见问题
1. **模型检测失败**: Ollama服务未启动
   - 解决: 运行 `ollama serve` 启动服务
2. **连接超时**: 防火墙阻止端口11434
   - 解决: 检查防火墙设置
3. **默认模型缺失**: 未下载mollysama/rwkv-7-g1:0.4B
   - 解决: 运行 `ollama pull mollysama/rwkv-7-g1:0.4B`

## 项目概述

NovelCraft是一个全方位的小说创作与管理系统，旨在帮助作者从多个维度管理小说设定、剧情发展、人物关系等内容，并通过AI辅助生成、分析和续写功能，确保故事的连贯性和完整性。

## 系统架构

### 技术栈
- **后端**: Python 3.9+ + FastAPI + SQLite/MongoDB
- **前端**: React + Electron + Ant Design
- **AI引擎**: 基于大型语言模型的文本生成与分析
- **可视化**: D3.js + Timeline.js + Leaflet.js

### 核心模块
1. **数据管理模块** - 项目管理、版本控制、数据同步
2. **设定管理模块** - 世界观、修炼体系、政治经济系统
3. **人物管理模块** - 人物档案、关系网络、成长轨迹
4. **剧情管理模块** - 主线支线、章节管理、时间线
5. **AI辅助模块** - 设定生成、剧情分析、续写引擎
6. **用户界面模块** - 仪表盘、编辑器、可视化工具

## 项目结构

```
NovelCraft/
├── backend/                    # 后端服务
│   ├── app/                   # 应用主目录
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   ├── utils/            # 工具函数
│   │   └── main.py           # 应用入口
│   ├── tests/                # 测试文件
│   ├── requirements.txt      # Python依赖
│   └── alembic/              # 数据库迁移
├── frontend/                  # 前端应用
│   ├── src/                  # 源代码
│   │   ├── components/       # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   ├── styles/          # 样式文件
│   │   └── App.js           # 应用入口
│   ├── public/              # 静态资源
│   ├── package.json         # 前端依赖
│   └── electron.js          # Electron主进程
├── ai_engine/                # AI引擎
│   ├── generators/          # 内容生成器
│   ├── analyzers/           # 内容分析器
│   ├── models/              # AI模型
│   └── utils/               # AI工具函数
├── docs/                     # 文档
├── scripts/                  # 构建脚本
└── README.md                # 项目说明
```

## 数据模型

### 核心实体
- **Project**: 项目基本信息
- **WorldSetting**: 世界观设定
- **CultivationSystem**: 修炼体系
- **Character**: 人物信息
- **Faction**: 势力组织
- **Plot**: 剧情内容
- **Chapter**: 章节内容
- **Timeline**: 时间线事件

### 关系模型
- **CharacterRelation**: 人物关系
- **FactionRelation**: 势力关系
- **EventAssociation**: 事件关联

## 功能特性

### 已实现功能
- [x] 项目基础架构
- [x] 数据模型设计
- [x] API接口框架
- [x] 项目管理功能
- [x] 用户界面框架
- [x] 仪表盘
- [x] 项目列表和详情
- [x] **AI多平台支持** - OpenAI、Claude、Ollama、自定义接口
- [x] **AI助手界面** - 智能对话、内容生成、设定创建
- [x] **系统设置管理** - AI配置、通用设置、数据管理
- [x] **AI服务抽象层** - 统一的AI调用接口
- [x] **人物管理功能** - 完整的人物档案管理系统
- [x] **章节管理功能** - 章节列表、编辑、状态跟踪
- [x] **世界设定管理** - 世界观、地理、历史、文化设定
- [x] **势力管理功能** - 势力组织、关系网络、等级体系
- [x] **剧情管理功能** - 剧情线索、发展跟踪、冲突设定
- [x] **修炼体系功能** - 修炼等级、方法配置、境界设定
- [x] **关系网络功能** - 人物关系图谱、关系类型分类
- [x] **时间线功能** - 事件时间轴、重要节点标记
- [x] **🆕 Ollama模型自动检测** - 实时检测本地模型、动态选择界面
- [ ] 章节富文本编辑器
- [ ] 数据导入导出功能

### 核心功能
1. **项目管理**: 创建、编辑、删除、导入导出小说项目
2. **设定管理**: 世界观、修炼体系、政治经济系统管理
3. **人物管理**: 人物档案、关系网络、成长轨迹追踪
4. **剧情管理**: 主线支线管理、章节编辑、时间线可视化
5. **AI辅助**: 设定生成、剧情分析、续写建议、一致性检查
6. **可视化**: 关系图谱、时间线、地图交互

### AI功能特性 🤖

#### 多平台AI支持
- **OpenAI**: 支持GPT-3.5、GPT-4等模型
- **Claude**: 支持Claude 3 Haiku、Sonnet、Opus
- **Ollama**: 支持本地部署的开源模型
- **自定义接口**: 兼容OpenAI API格式的自定义服务

#### 智能内容生成
- **世界设定生成**: 根据关键词生成详细的世界观设定
- **人物角色生成**: 创建具有完整背景的角色档案
- **剧情大纲生成**: 生成结构化的故事情节
- **章节续写**: 基于已有内容智能续写
- **一致性检查**: 检测内容中的逻辑矛盾

#### AI助手对话
- **实时交互**: 与AI进行自然语言对话
- **上下文理解**: 保持对话连贯性
- **创作指导**: 提供写作建议和技巧
- **问题解答**: 回答创作相关问题

#### 灵活配置
- **动态切换**: 实时切换不同AI提供商
- **参数调节**: 自定义温度、Token数等参数
- **状态监控**: 实时显示AI服务连接状态
- **错误处理**: 完善的异常处理和重试机制

## AI配置说明

### 支持的AI平台

#### 1. OpenAI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: GPT-3.5 Turbo, GPT-4, GPT-4 Turbo
- **获取方式**: 访问 [OpenAI官网](https://platform.openai.com/) 获取API Key

#### 2. Claude (Anthropic)
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Claude 3 Haiku, Claude 3 Sonnet, Claude 3 Opus
- **获取方式**: 访问 [Anthropic官网](https://console.anthropic.com/) 获取API Key

#### 3. 智谱AI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: GLM-4, GLM-3 Turbo, ChatGLM3-6B
- **获取方式**: 访问 [智谱AI官网](https://open.bigmodel.cn/) 获取API Key

#### 4. 硅基流动
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: DeepSeek-Chat, Qwen-Turbo, Yi-Large
- **获取方式**: 访问 [硅基流动官网](https://siliconflow.cn/) 获取API Key

#### 5. Google AI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Gemini-Pro, Gemini-Pro-Vision, Gemini-Ultra
- **获取方式**: 访问 [Google AI Studio](https://makersuite.google.com/) 获取API Key

#### 6. Grok (xAI)
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Grok-Beta, Grok-1
- **获取方式**: 访问 [xAI官网](https://x.ai/) 获取API Key

#### 7. Ollama (本地部署) 🆕 **[默认配置]**
- **配置项**: 服务地址, 模型名称
- **默认设置**:
  - **默认服务地址**: http://localhost:11434
  - **默认模型**: mollysama/rwkv-7-g1:0.4B
- **支持模型**: Llama2, Mistral, CodeLlama, RWKV等开源模型
- **🔥 新功能**: **自动模型检测** - 系统会自动检测本地已安装的Ollama模型
- **安装方式**:
  1. 下载安装 [Ollama](https://ollama.ai/)
  2. 运行 `ollama pull mollysama/rwkv-7-g1:0.4B` 下载默认模型
  3. 启动服务 `ollama serve`
- **智能特性**:
  - 🔍 实时检测本地可用模型
  - 📊 显示模型大小和详细信息
  - 🔄 一键刷新模型列表
  - ⚡ 动态模型选择界面

#### 8. 自定义OpenAI兼容接口
- **配置项**: API Key, Base URL, 模型名称
- **适用场景**: 支持OpenAI API格式的第三方服务
- **示例**: OneAPI, FastChat, vLLM等

### 配置步骤
1. 启动应用后，访问 **系统设置** → **AI配置**
2. 选择要使用的AI提供商
3. 填入相应的配置信息（API Key、服务地址等）
4. 点击 **测试连接** 验证配置
5. 保存设置并切换到对应提供商

## 安装与运行

### 一键安装和启动（推荐）

#### 完整安装流程
1. **安装依赖**: 双击 `Install-Dependencies.bat`
   - 自动检查Python和Node.js环境
   - 安装所有必需的依赖包
   - 显示详细的安装进度

2. **启动系统**: 双击 `Start-System.bat`
   - 自动环境检查和依赖验证
   - 启动后端和前端服务
   - 自动打开浏览器

3. **停止系统**: 双击 `Stop-System.bat`
   - 安全停止所有服务
   - 清理相关进程

### 手动安装（开发者）

#### 后端安装
```bash
cd backend
pip install -r requirements.txt
```

#### 前端安装
```bash
cd frontend
npm install
```

### 手动启动服务

#### 启动后端
```bash
cd backend
python run.py
```

#### 启动前端
```bash
cd frontend
npm start
```

#### Electron桌面应用
```bash
cd frontend
npm run electron-dev
```

### 访问地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

### 服务管理

#### 启动服务
- 双击 `Start-System.bat` - 完整启动流程
- 或手动启动后端和前端服务

#### 停止服务
- 双击 `Stop-System.bat` - 安全停止所有服务
- 或在启动窗口按 `Ctrl+C` 停止

#### 重新安装依赖
- 双击 `Install-Dependencies.bat` - 重新安装所有依赖

## 开发指南

### 代码规范
- Python: 遵循PEP 8规范
- JavaScript: 使用ESLint + Prettier
- 组件命名: PascalCase
- 文件命名: kebab-case

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 使用说明

### 基本操作流程

1. **创建项目**
   - 在项目管理页面点击"新建项目"
   - 填写项目基本信息（名称、类型、简介等）
   - 选择合适的项目模板（可选）

2. **设定管理**
   - 创建世界观设定（地理、历史、文化等）
   - 设计修炼体系（等级、能力、规则等）
   - 建立政治经济系统

3. **人物管理**
   - 创建主要角色和配角
   - 设定人物属性、性格、背景
   - 建立人物关系网络

4. **剧情规划**
   - 设计主线和支线剧情
   - 创建章节大纲
   - 安排时间线和事件

5. **内容创作**
   - 使用章节编辑器写作
   - 利用AI助手辅助创作
   - 进行一致性检查

### 主要功能模块

#### 1. 项目仪表盘
- 项目概览和统计信息
- 最近活动和进度跟踪
- 快速操作入口

#### 2. 项目管理
- 项目创建、编辑、删除
- 项目复制和模板功能
- 项目导入导出

#### 3. 内容管理
- 人物档案管理
- 势力组织管理
- 剧情线索管理
- 章节内容管理

#### 4. 设定管理
- 世界观设定
- 修炼体系设计
- 时间线管理
- 关系网络可视化

#### 5. AI辅助工具
- 智能设定生成
- 剧情分析建议
- 内容续写辅助
- 一致性自动检查

### 技术特性

- **跨平台支持**: 基于Electron的桌面应用
- **实时保存**: 自动保存和版本控制
- **数据安全**: 本地存储和云端备份
- **可视化**: 关系图谱和时间线展示
- **AI集成**: 智能创作辅助功能

## 开发路线图

### v1.0 (当前版本)
- [x] 基础架构搭建
- [x] 项目管理功能
- [x] 用户界面框架
- [x] 人物管理完善
- [x] 基础AI功能
- [x] 章节管理功能
- [x] 世界设定管理
- [x] 修炼体系设计器
- [x] 势力管理系统
- [x] 剧情管理系统
- [x] 关系网络管理
- [x] 时间线管理
- [x] 🆕 Ollama模型自动检测功能

### v1.1 (计划中)
- [ ] 富文本章节编辑器
- [ ] 数据导入导出功能
- [ ] 关系网络可视化图表
- [ ] 时间线可视化图表

### v1.2 (计划中)
- [ ] 高级AI功能
- [ ] 多语言支持
- [ ] 插件系统
- [ ] 云端同步

### v2.0 (远期规划)
- [ ] 移动端应用
- [ ] 协作功能
- [ ] 发布平台集成
- [ ] 专业版功能

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。


额外补充

一、AI助手功能模块设计（高扩展性）
1. 编剧AI（Director AI）
功能：根据用户给定的方向和命题，生成详细的大纲、世界观、人物设定、剧情主线等。
输入：命题、方向、主题关键词
输出：大纲、设定、主线剧情、章节规划
2. 剧情写作AI（Writer AI，RWKV为主）
功能：结合主题、大纲、时间线、设定等，分章节生成详细剧情文本。
输入：大纲、章节规划、设定、时间线
输出：章节详细内容
3. 总结AI（Summarizer AI）
功能：对每章节、每卷（部）进行总结，生成下一卷宗的前言。
输入：章节内容、卷内容
输出：章节总结、卷总结、前言
4. 读者AI（Reader AI）
功能：模拟读者对内容进行正向/负向评价，提出建议，辅助编剧AI和写作AI调整剧情和文风。
输入：章节内容、卷内容、总结
输出：评价、建议、反馈
5. AI协作与反馈机制
功能：各AI模块之间可自动传递内容，形成闭环反馈，支持人工干预和二次编辑。
输入/输出：各AI模块的输入输出

二、系统架构扩展建议
AI服务抽象层：所有AI助手通过统一接口调用，便于后续扩展新AI类型。
任务队列与流程引擎：支持多AI协作的任务流转与状态管理。
可视化流程管理：前端可视化展示AI协作流程、内容流转和反馈。

三、README.md结构补充建议
新增：AI编剧协作系统说明
## 致谢

感谢所有为NovelCraft项目做出贡献的开发者和用户。

## AI编剧协作系统

### 功能概述

本系统集成多种AI助手，协同完成小说从命题到完本的全过程创作。各AI助手分工明确，互相协作，支持人工干预和反馈优化。

### 主要AI助手模块

1. **编剧AI（Director AI）**
   - 负责根据命题生成大纲、设定、主线剧情。
   - 输入：命题、方向、主题
   - 输出：大纲、设定、主线剧情

2. **剧情写作AI（Writer AI，RWKV）**
   - 负责分章节生成详细剧情。
   - 输入：大纲、章节规划、设定、时间线
   - 输出：章节内容

3. **总结AI（Summarizer AI）**
   - 负责章节、卷的总结，生成前言。
   - 输入：章节/卷内容
   - 输出：总结、前言

4. **读者AI（Reader AI）**
   - 负责模拟读者评价，提出正负反馈和建议。
   - 输入：章节/卷内容、总结
   - 输出：评价、建议

5. **AI协作与反馈机制**
   - 各AI模块自动流转内容，支持人工干预和二次编辑。

### 工作流程

1. 用户输入命题/方向
2. 编剧AI生成大纲、设定、主线
3. 剧情写作AI分章节生成内容
4. 总结AI对章节/卷进行总结，生成前言
5. 读者AI给出评价和建议
6. 编剧AI/写作AI根据反馈调整内容
7. 循环迭代，直至小说完本

### 扩展性说明

- 所有AI助手通过统一接口调用，便于扩展新AI类型
- 支持多AI协作、任务流转、人工干预
- 可视化流程管理，便于内容追踪和优化

### 主要调用方式

- 通过系统设置界面配置各AI助手参数
- 在创作流程中选择AI助手自动/手动生成内容
- 支持一键生成、逐步生成、人工编辑与反馈

### 代码结构建议
ai_engine/
├── director/ # 编剧AI相关逻辑
│ └── director_ai.py
├── writer/ # 剧情写作AI（RWKV等）
│ └── writer_ai.py
├── summarizer/ # 总结AI
│ └── summarizer_ai.py
├── reader/ # 读者AI
│ └── reader_ai.py
├── workflow/ # AI协作与流程管理
│ └── workflow_engine.py
└── utils/ # 工具函数


- 每个AI助手为独立子模块，便于维护和扩展
- 统一接口，支持多模型、多平台切换
- 支持任务队列和流程引擎，便于多AI协作

### 备注

- 推荐优先支持ollama、RWKV等本地模型，保障数据安全和隐私
- 支持zhipuAI、deepseek、硅基流动等云端模型作为补充
- 后续可扩展更多AI助手类型和协作模式